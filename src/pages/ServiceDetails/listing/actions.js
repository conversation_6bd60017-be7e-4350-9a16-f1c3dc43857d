import { createAction } from '@reduxjs/toolkit';
import { STATE_REDUCER_KEY } from './constants';

export const ACTION_TYPES = {

  FETCH_SERVICE_DETAILS_FOR_EMPLOYEE: `${STATE_REDUCER_KEY}/FETCH_SERVICE_DETAILS_FOR_EMPLOYEE`,
  FETCH_SERVICE_DETAILS_FOR_EMPLOYEE_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICE_DETAILS_FOR_EMPLOYEE_REQUEST`,
  FETCH_SERVICE_DETAILS_FOR_EMPLOYEE_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SERVICE_DETAILS_FOR_EMPLOYEE_SUCCESS`,
  FETCH_SERVICE_DETAILS_FOR_EMPLOYEE_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICE_DETAILS_FOR_EMPLOYEE_FAILURE`,

  FETCH_SERVICE_NAME_DETAILS: `${STATE_REDUCER_KEY}/FETCH_SERVICE_NAME_DETAILS`,
  FETCH_SERVICE_NAME_DETAILS_REQUEST: `${STATE_REDUCER_KEY}/FETCH_SERVICE_NAME_DETAILS_REQUEST`,
  FETCH_SERVICE_NAME_DETAILS_SUCCESS: `${STATE_REDUCER_KEY}/FETCH_SERVICE_NAME_DETAILS_SUCCESS`,
  FETCH_SERVICE_NAME_DETAILS_FAILURE: `${STATE_REDUCER_KEY}/FETCH_SERVICE_NAME_DETAILS_FAILURE`

};
export const fetchServiceDetailsForEmployee = createAction(ACTION_TYPES.FETCH_SERVICE_DETAILS_FOR_EMPLOYEE);
export const fetchServiceNameDetails = createAction(ACTION_TYPES.FETCH_SERVICE_NAME_DETAILS);
