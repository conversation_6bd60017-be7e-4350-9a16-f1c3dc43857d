import { API_URL, REQUEST_METHOD } from 'common';
import { ACTION_TYPES } from './actions';

export const fetchServiceDetailsForEmployee = ({ lbType }) => {
  return {
    url: API_URL.COMMON.SERVICES.replace(':query', lbType),
    method: REQUEST_METHOD.GET,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICE_DETAILS_FOR_EMPLOYEE_REQUEST,
        ACTION_TYPES.FETCH_SERVICE_DETAILS_FOR_EMPLOYEE_SUCCESS,
        ACTION_TYPES.FETCH_SERVICE_DETAILS_FOR_EMPLOYEE_FAILURE
      ]
    }
  };
};

export const fetchServiceNameDetails = (params, data) => {
  return {
    url: API_URL.COMMON.FETCH_SERVICE_NAME_DETAILS,
    method: REQUEST_METHOD.POST,
    payload: {
      types: [
        ACTION_TYPES.FETCH_SERVICE_NAME_DETAILS_REQUEST,
        ACTION_TYPES.FETCH_SERVICE_NAME_DETAILS_SUCCESS,
        ACTION_TYPES.FETCH_SERVICE_NAME_DETAILS_FAILURE
      ],
      params,
      data
    }
  };
};
