import SearchIcon from 'assets/SearchIcon';
import {
  But<PERSON>,
  FormController, FormWrapper, IconButton, Input, InputGroup, InputRightElement, t
} from 'common/components';
import { FILTER_TYPE } from 'pages/common/constants';
import {
  getAllSeats, getCorrespondTypeDropdown, getCounterOperator, getModeOfDispatch, getSeats, getUserInfo
} from 'pages/common/selectors';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import * as commonActions from 'pages/common/actions';
import { useForm } from 'react-hook-form';
import AdvanceSearch from 'pages/Dispatch-latest/SendLetter/details/components/AdvanceSearch';

const styles = {
  search: {
    width: '420px',
    input: {
      borderRadius: '7px',
      border: '1px solid #E2E8F0',
      height: '57px'
    },
    button: {
      background: 'none'
    }
  }
};

const OutBoxFilter = ({
  fetchCounterOperator, counterOperatorDropdown,
  seatsDropdown, fetchSeats, userInfo,
  setDraftSendLetterPreviewListParams,
  draftSendLetterPreviewListParams,
  allSeatsDropdown, fetchSeatsByOffice, fetchModeOfDispatch,
  modeOfDispatchDetails, fetchCorrespondTypeDetails, CorrespondTypeDropdown
}) => {
  const {
    control,
    setValue
  } = useForm({
    mode: 'all',
    defaultValues: {
    }
  });

  const [post, setPost] = useState([]);
  const [search, setSearch] = useState('');

  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (seatsDropdown?.length > 0) {
      const postArray = seatsDropdown?.filter((item) => item?.name !== null && item?.name !== '' && item?.name !== ' ');
      const updatedData = postArray?.map((obj) => {
        return {
          name: obj.postName,
          id: obj.postId,
          employeeName: obj?.employeeName,
          penNo: obj?.penNo
        };
      });
      setPost(updatedData);
    } else {
      setPost([]);
    }
  }, [seatsDropdown]);

  useEffect(() => {
    fetchCounterOperator();
    if (userInfo?.officeId) {
      fetchSeatsByOffice({ officeId: userInfo?.officeId });
    }
  }, [userInfo]);

  const [isInitial, setIsInitial] = useState(true);

  const getSeatList = () => {
    return isInitial ? allSeatsDropdown?.filter((item) => item?.name !== null && item?.name !== '' && item?.name !== ' ') : post;
  };

  useEffect(() => {
    setIsInitial(true);
    fetchModeOfDispatch();
    fetchCorrespondTypeDetails();
  }, []);

  const triggerSearch = (field, data) => {
    switch (field) {
      case FILTER_TYPE.SEARCH_KEY_WORD:
        setDraftSendLetterPreviewListParams({
          ...draftSendLetterPreviewListParams,
          keyword: data
        });
        break;
      case FILTER_TYPE.DEPARTMENT:
        if (data) {
          setIsInitial(false);
          fetchSeats({ functionalGroupId: data?.functionalGroupId, officeId: userInfo?.id });
          setDraftSendLetterPreviewListParams({
            ...draftSendLetterPreviewListParams,
            functionalGroupId: data?.id
          });
        } else {
          setIsInitial(true);
          setValue('seat', '');
          setPost([]);
          setDraftSendLetterPreviewListParams({
            ...draftSendLetterPreviewListParams,
            functionalGroupId: null,
            createdSeat: null
          });
        }
        break;
      case FILTER_TYPE.SEAT:
        if (data) {
          setDraftSendLetterPreviewListParams({
            ...draftSendLetterPreviewListParams,
            createdSeat: data?.name?.trim()
          });
        } else {
          setDraftSendLetterPreviewListParams({
            ...draftSendLetterPreviewListParams,
            createdSeat: null
          });
        }
        break;
      default:
        break;
    }
  };

  return (
    <div>
      <div className="pt-2 pb-5">
        <FormWrapper px={false} py={false}>
          <div className="lg:col-span-2 col-span-12">
            <FormController
              name="department"
              type="select"
              label={t('department')}
              placeholder={t('select')}
              control={control}
              optionKey="name"
              isClearable
              handleChange={(data) => triggerSearch(FILTER_TYPE.DEPARTMENT, data)}
              options={counterOperatorDropdown || []}
            />
          </div>
          <div className="lg:col-span-2 col-span-12">
            <FormController
              name="seat"
              type="select"
              label={t('seat')}
              placeholder={t('searchHere')}
              control={control}
              options={getSeatList()}
              optionKey="id"
              isClearable
              handleChange={(data) => {
                triggerSearch(FILTER_TYPE.SEAT, data);
              }}
            />
          </div>
          <div className="lg:col-span-2 col-span-12">
            <InputGroup style={styles.search}>
              <Input
                placeholder={t('searchHere')}
                style={styles.search.input}
                value={search}
                onChange={(event) => {
                  if (event.target.value) {
                    setSearch(event.target.value);
                  } else {
                    setSearch('');
                    setDraftSendLetterPreviewListParams({
                      ...draftSendLetterPreviewListParams,
                      keyword: null,
                      page: 0
                    });
                  }
                }}
                onKeyDown={(event) => {
                  if (event.key === 'Enter') {
                    triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search);
                  }
                }}
              />
              <InputRightElement>
                {
                  search
                  && (
                    <div
                      onClick={() => {
                        setSearch('');
                        setDraftSendLetterPreviewListParams({
                          ...draftSendLetterPreviewListParams,
                          keyword: null,
                          page: 0
                        });
                      }}
                      aria-hidden
                      className="cursor-pointer"
                      style={{
                        fontSize: '22px',
                        color: '#555',
                        position: 'absolute',
                        right: '55px',
                        top: '75%',
                        transform: 'translateY(-50%)'
                      }}
                    >
                      x
                    </div>
                  )
                }
                <IconButton
                  onClick={() => triggerSearch(FILTER_TYPE.SEARCH_KEY_WORD, search)}
                  icon={<SearchIcon color="#fff" />}
                  colorScheme="pink"
                  variant="solid"
                  isRound
                  style={{
                    position: 'absolute',
                    top: '70%',
                    transform: 'translateY(-50%)',
                    padding: '0 8px'
                  }}
                />

              </InputRightElement>
            </InputGroup>
          </div>
          <div className="lg:col-span-2 col-span-12 pt-[6px] pl-[200px]">
            <Button
              onClick={handleOpen}
              size="lg"
              colorScheme="pink"
              style={{
                height: '44px', borderRadius: '25px', width: '153px', fontSize: '14px'
              }}
            >
              {t('advanceSearch')}
            </Button>
          </div>
          <div className="lg:col-span-2 col-span-12 pt-[6px] pl-[150px]">
            <Button
              onClick={() => {
                setValue('department', '');
                setValue('seat', '');
                setSearch('');
                setDraftSendLetterPreviewListParams({
                  ...draftSendLetterPreviewListParams,
                  keyword: null,
                  functionalGroupId: null,
                  createdSeat: null,
                  modeOfDispatchId: null,
                  draftType: null,
                  date: null,
                  fromDate: null,
                  toDate: null,
                  dispatchStatus: null,
                  page: 0
                });
              }}
              size="lg"
              colorScheme="primary"
              style={{
                height: '44px', borderRadius: '25px', width: '153px', fontSize: '14px'
              }}
            >
              {t('reset')}
            </Button>
          </div>
        </FormWrapper>
      </div>
      {open && (
        <AdvanceSearch
          handleClose={handleClose}
          open={open}
          modeOfDispatchDetails={modeOfDispatchDetails}
          CorrespondTypeDropdown={CorrespondTypeDropdown}
          setFilter={setDraftSendLetterPreviewListParams}
          filterParams={draftSendLetterPreviewListParams}
          from="outbox"
        />
      )}
    </div>
  );
};
const mapStateToProps = createStructuredSelector({
  counterOperatorDropdown: getCounterOperator,
  seatsDropdown: getSeats,
  userInfo: getUserInfo,
  allSeatsDropdown: getAllSeats,
  modeOfDispatchDetails: getModeOfDispatch,
  CorrespondTypeDropdown: getCorrespondTypeDropdown
});

const mapDispatchToProps = (dispatch) => ({
  fetchCounterOperator: (data) => dispatch(commonActions.fetchCounterOperator(data)),
  fetchSeats: (data) => dispatch(commonActions.fetchSeats(data)),
  fetchSeatsByOffice: (data) => dispatch(commonActions.fecthSeatsByOffice(data)),
  fetchModeOfDispatch: (data) => dispatch(commonActions.fetchModeOfDispatch(data)),
  fetchCorrespondTypeDetails: () => dispatch(commonActions.fetchCorrespondTypeDetails())
});

export default connect(mapStateToProps, mapDispatchToProps)(OutBoxFilter);
