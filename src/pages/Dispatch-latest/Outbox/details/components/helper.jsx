import { getSumOfRupees } from '../../../helper';

export const handleRupeesChange = (index, val, inputValue, setStampValue, setSumOfStampValue) => {
  const updateArray = (array) => {
    if (val?.length < 8) {
      return array.map((item, i) => (i === index
        ? { ...item, outputValue: Number(inputValue * val), value: Number(val) }
        : item));
    }
    return array;
  };

  setStampValue((prevStampValue) => {
    const updatedArray = updateArray(JSON.parse(JSON.stringify(prevStampValue)));
    getSumOfRupees(updatedArray, setSumOfStampValue);
    return updatedArray;
  });
};
