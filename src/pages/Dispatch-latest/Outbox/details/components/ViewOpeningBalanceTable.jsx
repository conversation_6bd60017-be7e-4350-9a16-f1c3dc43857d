import { Button, t } from 'common/components';
import { getStampDenomination, getStampInventoryViewListParams, getStampInventoyrTableList } from 'pages/Dispatch-latest/selector';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as commonSliceActions } from 'pages/common/slice';
import { CommonTable } from 'common/components/Table';
import { getTableLoader } from 'pages/common/selectors';
import _ from 'lodash';
import { actions as sliceActions } from '../../../slice';
import * as actions from '../../../actions';

const ViewOpeningBalanceTable = ({
  fetchInventoryViewTableList,
  setStampInventoryViewListParams, setTableLoader, inventoryViewParams,
  tableLoader, stampInventoryList, handleClose = () => { },
  stampDenominationDetails, getCashDenominationTypes
}) => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [page, setPage] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);

  useEffect(() => {
    getCashDenominationTypes();
  }, []);

  useEffect(() => {
    if (inventoryViewParams) {
      setTableLoader({ loading: true, id: 'inventory-view-table' });
      fetchInventoryViewTableList();
    }
  }, [inventoryViewParams]);

  const onPageClick = (data) => {
    setPage(data);
    setStampInventoryViewListParams({
      ...inventoryViewParams,
      page: data
    });
  };

  useEffect(() => {
    if (stampInventoryList) {
      setTableLoader({ loading: false, id: 'inventory-view-table' });
      if (Object.keys(stampInventoryList)?.length > 0) {
        setTableData(stampInventoryList?.content);
        setTotalItems(Number(`${stampInventoryList.totalPages}0`));
        setNumberOfElements(Number(stampInventoryList.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [stampInventoryList]);

  const handleStampTypeId = (fileData) => {
    let stampTypeId;
    if (fileData?.row) {
      const cellData = fileData?.row;
      stampTypeId = (
        <div>{_.filter(stampDenominationDetails, (el) => el?.id === cellData?.stampTypeId)[0]?.cashDenominationValue}</div>
      );
    }
    return <div className="block text-[#3C4449] text-[14px] font-medium">{stampTypeId}</div>;
  };

  const handleCount = (fileData) => {
    let count;
    if (fileData?.row) {
      const cellData = fileData?.row;
      count = (
        <div>{cellData?.count} Nos.</div>
      );
    }
    return <div className="block text-[#3C4449] text-[14px] font-medium">{count}</div>;
  };

  const handleAmount = (fileData) => {
    let amount;
    if (fileData?.row) {
      const cellData = fileData?.row;
      amount = (
        <div>{cellData?.amount} Rs.</div>
      );
    }
    return <div className="block text-[#3C4449] text-[14px] font-medium">{amount}</div>;
  };

  const columns = [

    {
      header: t('stampValue'),
      field: 'stampTypeId',
      alignment: 'left',
      cell: (field) => handleStampTypeId(field)
    },
    {
      header: t('stampCount'),
      field: 'count',
      alignment: 'left',
      cell: (field) => handleCount(field)
    },

    {
      header: t('totalAmount'),
      field: 'amount',
      alignment: 'left',
      cell: (field) => handleAmount(field)
    }

  ];

  return (
    <>
      <div>
        <CommonTable
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          onPageClick={onPageClick}
          itemsPerPage={10}
          totalItems={totalItems}
          currentPage={page}
          numberOfElements={numberOfElements}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'inventory-view-table'}
        />
      </div>
      <div className="text-[#09327B] text-[14px] font-normal flex justify-end gap-3 pt-3 pb-3">{t('grandTotal')} <span className="font-bold">{stampInventoryList?.content?.reduce((n, { amount }) => n + amount, 0)} Rs</span></div>
      <div className="flex justify-end gap-3 pt-3 pb-3">
        <Button variant="primary_outline" size="sm" type="submit" form="stamp-inventory-view-form" onClick={handleClose}>
          {t('back')}
        </Button>

      </div>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  inventoryViewParams: getStampInventoryViewListParams,
  tableLoader: getTableLoader,
  stampInventoryList: getStampInventoyrTableList,
  stampDenominationDetails: getStampDenomination
});

const mapDispatchToProps = (dispatch) => ({
  fetchInventoryViewTableList: (data) => dispatch(actions.fetchInventoryViewTableList(data)),
  setStampInventoryViewListParams: (data) => dispatch(sliceActions.setStampInventoryViewListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  getCashDenominationTypes: (data) => dispatch(actions.getCashDenominationTypes(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ViewOpeningBalanceTable);
