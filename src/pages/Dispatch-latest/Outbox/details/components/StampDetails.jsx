import { But<PERSON>, Spinner } from '@ksmartikm/ui-components';
import { t } from 'common/components';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { getClaimAdvanceAmountDetails, getStampDenomination, getStampDetails } from 'pages/Dispatch-latest/selector';
import { useForm } from 'react-hook-form';
import { NUMBER_ONLY } from 'common/regex';
import { CASH_DECLARATION_CHARACTERRS } from 'pages/common/constants';
import '../../../Style.css';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import { actions as commonSliceActions } from 'pages/common/slice';
import { getTableLoader } from 'pages/common/selectors';
import { getFinancialYearStart } from 'utils/date';
import * as actions from '../../../actions';

const StampDetails = ({
  handleClose = () => { }, fetchStampDetails,
  stampDetails = [], stampDenominationDetails,
  saveStampDetails, setTableLoader, tableLoader,
  // amountData,
  setAlertAction,
  // claimAdvanceAmountDetails,
  cashInHand
}) => {
  const [stampValue, setStampValue] = useState([]);
  const [sumOfStampValue, setSumOfStampValue] = useState(0);

  const { handleSubmit } = useForm({
    mode: 'all'
  });

  useEffect(() => {
    setTableLoader({ loading: true, id: 'stamp-table' });
    fetchStampDetails();
  }, []);

  useEffect(() => {
    if (stampDetails?.length > 0) {
      const copyArray = JSON.parse(JSON.stringify(stampDetails));
      const sortedArray = copyArray?.sort((a, b) => {
        return a.stampTypeId - b.stampTypeId;
      });
      setStampValue(sortedArray);
    }
  }, [stampDetails]);

  const onSubmitForm = () => {
    if (sumOfStampValue > cashInHand) {
      setAlertAction({
        open: true,
        variant: 'error',
        message: 'total amount must be less than claim',
        title: t('error'),
        backwardActionText: t('ok')
      });
    } else {
      const filteredArrayOfStampDetails = _.map(stampValue, (o) => {
        const stampTypeId = o?.stampTypeId || 0;
        const stampCount = Number(o?.value || 0);
        const totalAmount = Number(o?.outputValue || 0);

        return {
          stampTypeId,
          stampCount,
          totalAmount,
          finYear: Number(getFinancialYearStart())
        };
      });
      saveStampDetails({
        payloadArray: filteredArrayOfStampDetails,
        officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID)
      });
      handleClose();
    }
  };

  const getSumOfRupees = (updatedArray) => {
    const total = updatedArray.reduce((n, { outputValue }) => n + outputValue, 0);
    setSumOfStampValue(total);
  };

  const handleRupeesChange = (index, val, inputValue) => {
    if (val?.length < 8) {
      setStampValue((prevStampValue) => {
        const newArray = JSON.parse(JSON.stringify(prevStampValue));
        newArray[index].outputValue = Number(inputValue * val);
        newArray[index].value = Number(val);
        getSumOfRupees(newArray);
        return newArray;
      });
    } else {
      setStampValue((prevStampValue) => {
        getSumOfRupees(prevStampValue);
        return prevStampValue;
      });
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmitForm)} className="form">
      <div className="p-5 max-h-[500px] overflow-y-auto">
        <table className="striped-table">
          <thead>
            <tr>
              <th>{t('stampType')}</th>
              <th>{t('stampInHand')}</th>
              <th>{t('stampCount')}</th>
              <th>{t('totalAmount')}</th>
            </tr>
          </thead>
          <tbody>
            {
              tableLoader?.loading && tableLoader?.id === 'stamp-table'
                ? (
                  <tr>
                    <td colSpan={5}>
                      <div style={{ marginTop: '100px', marginLeft: '300px', paddingBottom: '100px' }}>
                        <Spinner />
                      </div>
                    </td>
                  </tr>
                )

                : stampValue?.length > 0
                && stampValue?.map((val, i) => (

                  <tr key={val?.id}>
                    <td className="text-[#3C4449] text-[14px] text-center font-medium">{_.filter(stampDenominationDetails, (el) => el?.id === val?.stampTypeId)[0]?.cashDenominationValue}</td>
                    <td className="text-[#3C4449] text-[14px] text-center font-medium">{val?.count}</td>
                    <td aria-label="input" className="text-center">
                      <input
                        id={val?.id}
                        type="number"
                        min="0"
                        onKeyUp={(event) => {
                          if (!NUMBER_ONLY.test(event.key)) {
                            event.preventDefault();
                          }
                        }}
                        onKeyDown={(e) => CASH_DECLARATION_CHARACTERRS?.includes(e.key)
                          && e.preventDefault()}
                        onWheel={(e) => e.target.blur()}
                        placeholder={val?.defaultValue}
                        name={_.filter(stampDenominationDetails, (el) => el?.id === val?.stampTypeId)[0]?.cashDenomination}
                        onChange={(e) => handleRupeesChange(
                          i,
                          e.target.value,
                          _.filter(stampDenominationDetails, (el) => el?.id === val?.stampTypeId)[0]?.cashDenominationValue
                        )}
                        value={val?.value}
                      />
                    </td>
                    <td className="text-[#3C4449] text-[14px] text-center font-medium">{val?.outputValue} Rs.</td>
                  </tr>
                ))
            }

          </tbody>
        </table>
      </div>

      <div className="flex">
        <div className="text-[#153171] text-[14px] font-normal flex justify-end gap-3 pt-3 pb-3 pl-[20px]">Cash In Hand<span aria-hidden="true"><span className="font-bold">{cashInHand} ₹</span></span></div>
        <div className="flex-grow text-[#09327B] text-[14px] font-normal flex justify-end gap-3 pt-3 pb-3">{t('grandTotal')} <span className="font-bold">{sumOfStampValue || 0} Rs</span></div>

      </div>
      <div>
        <div className="flex justify-end p-[18px]">
          <Button variant="primary_outline" size="sm" mr={3} onClick={handleClose}>
            {t('cancel')}
          </Button>
          <Button variant="primary" size="sm" type="submit">
            {t('save')}
          </Button>
        </div>
      </div>
    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  stampDetails: getStampDetails,
  stampDenominationDetails: getStampDenomination,
  tableLoader: getTableLoader,
  claimAdvanceAmountDetails: getClaimAdvanceAmountDetails
});

const mapDispatchToProps = (dispatch) => ({
  fetchStampDetails: (data) => dispatch(actions.fetchStampDetails(data)),
  saveStampDetails: (data) => dispatch(actions.saveStampDetails(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(StampDetails);
