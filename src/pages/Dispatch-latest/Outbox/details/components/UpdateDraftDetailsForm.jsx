import { But<PERSON>, <PERSON>rrorText, Spinner } from '@ksmartikm/ui-components';
import { getCurrentStampValue, getStampDenomination, getStampDetails } from 'pages/Dispatch-latest/selector';
import { getTableLoader, getUserInfo } from 'pages/common/selectors';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as commonSliceActions } from 'pages/common/slice';
import { NUMBER_ONLY } from 'common/regex';
import { CASH_DECLARATION_CHARACTERRS } from 'pages/common/constants';
import { t } from 'common/components';
import { getSumOfRupees } from 'pages/Dispatch-latest/helper';
import _ from 'lodash';
import * as actions from '../../../actions';
import '../../../Style.css';

const UpdateDraftDetailsForm = ({
  setOpenDraftUpdate = () => { }, setOpenEntryForm = () => { },
  tableLoader, setTableLoader, stampDetails, stampDenominationDetails,
  fetchStampDetails, updateStampDetails, currentStampValue,
  draftObj
}) => {
  const [stampValue, setStampValue] = useState([]);
  const [sumOfStampValue, setSumOfStampValue] = useState(0);
  const [error, setError] = useState('');

  useEffect(() => {
    setTableLoader({ loading: true, id: 'stamp-table' });
    fetchStampDetails();
  }, []);

  useEffect(() => {
    if (stampDetails?.length > 0) {
      const copyArray = JSON.parse(JSON.stringify(stampDetails));
      const sortedArray = copyArray?.sort((a, b) => {
        return a.stampTypeId - b.stampTypeId;
      });
      setStampValue(sortedArray);
    }
  }, [stampDetails]);

  const handleRupeesChange = (index, val, count) => {
    let newArray = [];
    newArray = JSON.parse(JSON.stringify(stampValue));

    if (val > count) {
      setError({ id: index, message: 'Invalid Stamp value' });
      newArray[index].value = '';
      newArray[index].outputValue = '';
      setStampValue(newArray);
    } else if (val === '') {
      setError({ id: index, message: '' });
      newArray[index].value = '';
      newArray[index].outputValue = '';
      setStampValue(newArray);
    } else {
      setError({ id: index, message: '' });
      newArray[index].value = Number(val);
      newArray[index].outputValue = (Number(val));
      setStampValue(newArray);
      getSumOfRupees(newArray, setSumOfStampValue, stampDenominationDetails);
    }
  };

  const updateStamp = () => {
    const filteredArrayOfStampDetails = [];

    _.filter(stampValue, (o) => {
      filteredArrayOfStampDetails.push({
        stampTypeId: o?.stampTypeId || 0,
        stampCount: Number(o?.value) || 0,
        amount: Number(o?.value) * Number(_.filter(stampDenominationDetails, (el) => el?.id === o?.stampTypeId)[0]?.cashDenominationValue || 0),
        dispatchDetails: {
          dispatchStatus: draftObj?.dispatchStatus,
          id: draftObj?.dispatchId,
          penNo: draftObj?.penNo,
          modeOfDispatchId: draftObj?.modeOfDispatchId,
          dispatchClerkName: draftObj?.dispatchClerkName,
          postId: draftObj?.postId
        }
      });
      return filteredArrayOfStampDetails;
    });

    updateStampDetails({ requestArray: filteredArrayOfStampDetails, dispatchId: draftObj?.dispatchId, fileNo: draftObj?.fileNo });
    setOpenEntryForm(false);
  };

  return (
    <form className="form" id="stamp-dispatch-form">
      <div className="p-5 max-h-[500px] overflow-y-auto">
        <table className="striped-table">
          <thead>
            <tr>
              <th>{t('stampDenomination')}</th>
              <th>{t('availableStock')}</th>
              <th>{t('usedStamp')}</th>
              <th>{t('stampNeed')}</th>
            </tr>
          </thead>
          <tbody>
            {
              tableLoader?.loading && tableLoader?.id === 'stamp-table'
                ? (
                  <tr>
                    <td colSpan={5}>
                      <div style={{ marginTop: '100px', marginLeft: '300px', paddingBottom: '100px' }}>
                        <Spinner />
                      </div>
                    </td>
                  </tr>
                )

                : stampValue?.length > 0
                && stampValue?.map((val, i) => (

                  <tr key={val?.id}>
                    <td className="text-[#3C4449] text-[14px] text-center font-medium">{_.filter(stampDenominationDetails, (el) => el?.id === val?.stampTypeId)[0]?.cashDenominationValue}</td>
                    <td className="text-[#3C4449] text-[14px] text-center font-medium">{val?.count}</td>
                    <td className="text-[#3C4449] text-[14px] text-center font-medium">{_.filter(currentStampValue, (el) => el?.stampTypeId === val?.stampTypeId)[0]?.count || 0}</td>
                    <td aria-label="input" className="text-center">
                      <input
                        id={val?.id}
                        type="number"
                        min="0"
                        onKeyUp={(event) => {
                          if (!NUMBER_ONLY.test(event.key)) {
                            event.preventDefault();
                          }
                        }}
                        onKeyDown={(e) => CASH_DECLARATION_CHARACTERRS?.includes(e.key)
                          && e.preventDefault()}
                        onWheel={(e) => e.target.blur()}
                        placeholder={val?.defaultValue}
                        name={_.filter(stampDenominationDetails, (el) => el?.id === val?.stampTypeId)[0]?.cashDenomination}
                        onChange={(e) => handleRupeesChange(
                          i,
                          e.target.value,
                          val?.count
                        )}
                        value={val?.value}
                        disabled={val?.count === 0}
                      />
                      {error?.id === i && error?.message && <ErrorText error={error?.message} />}
                    </td>
                  </tr>
                ))
            }

          </tbody>
        </table>
      </div>
      <div className="flex">
        <div className="pt-3 pb-3 pl-3">{t('currentStampValue')} <span className="font-bold">{currentStampValue?.length > 0 ? currentStampValue?.reduce((sum, item) => sum + item.amount, 0) : 0} Rs</span></div>
        <div className="flex-grow text-[#09327B] text-[14px] font-normal flex justify-end gap-3 pt-3 pb-3">{t('grandTotal')} <span className="font-bold">{sumOfStampValue} Rs</span></div>
      </div>
      <div className="flex-grow flex justify-end" />

      <div>
        <div className="flex justify-end p-[18px]">
          <Button
            variant="primary_outline"
            size="sm"
            mr={3}
            onClick={() => {
              setOpenDraftUpdate(true);
              setOpenEntryForm(false);
            }}
          >
            {t('back')}
          </Button>
          <Button variant="primary" size="sm" onClick={updateStamp}>
            {t('save')}
          </Button>
        </div>
      </div>

    </form>

  );
};

const mapStateToProps = createStructuredSelector({
  tableLoader: getTableLoader,
  stampDetails: getStampDetails,
  stampDenominationDetails: getStampDenomination,
  userInfo: getUserInfo,
  currentStampValue: getCurrentStampValue
});

const mapDispatchToProps = (dispatch) => ({
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  fetchStampDetails: (data) => dispatch(actions.fetchStampDetails(data)),
  updateStampDetails: (data) => dispatch(actions.updateStampDetails(data))

});

export default connect(mapStateToProps, mapDispatchToProps)(UpdateDraftDetailsForm);
