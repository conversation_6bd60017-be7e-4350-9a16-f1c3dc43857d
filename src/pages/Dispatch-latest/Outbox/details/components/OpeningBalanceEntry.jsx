import { Button } from '@ksmartikm/ui-components';
import { t } from 'common/components';
import React, { useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { getStampDenomination } from 'pages/Dispatch-latest/selector';
import { useForm } from 'react-hook-form';
import { NUMBER_ONLY } from 'common/regex';
import { CASH_DECLARATION_CHARACTERRS } from 'pages/common/constants';
import '../../../Style.css';
import _ from 'lodash';
import { STORAGE_KEYS } from 'common/constants';
import * as actions from '../../../actions';

const OpeningBalanceEntry = ({
  handleClose = () => { }, getCashDenominationTypes,
  stampDenominationDetails = [], saveStampOpeningDetails
}) => {
  const [stampValue, setStampValue] = useState([]);
  const [sumOfStampValue, setSumOfStampValue] = useState(0);

  const { handleSubmit } = useForm({
    mode: 'all'
  });

  useEffect(() => {
    getCashDenominationTypes();
  }, []);

  useEffect(() => {
    if (stampDenominationDetails?.length > 0) {
      setStampValue(stampDenominationDetails);
    }
  }, [stampDenominationDetails]);

  const onSubmitForm = () => {
    const filteredArrayOfStampDetails = [];

    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth() + 1;

    const finYear = currentMonth <= 3
      ? `${currentYear - 1}-${currentYear}`
      : `${currentYear}-${currentYear + 1}`;

    const firstYear = finYear.split('-')[0];

    _.filter(stampValue, (o) => {
      filteredArrayOfStampDetails.push({
        officeId: localStorage.getItem(STORAGE_KEYS.OFFICE_ID),
        stampTypeId: o?.id || 0,
        stampCount: o?.value || 0,
        totalAmount: o?.outputValue || 0,
        finYear: Number(firstYear)
      });
      return filteredArrayOfStampDetails;
    });
    saveStampOpeningDetails(filteredArrayOfStampDetails);
    handleClose();
  };

  const getSumOfRupees = (updatedArray) => {
    const total = updatedArray.reduce((n, { outputValue }) => n + outputValue, 0);
    setSumOfStampValue(total);
  };

  const handleRupeesChange = (index, val, inputValue) => {
    if (val?.length < 8) {
      setStampValue((prevStampValue) => {
        const newArray = JSON.parse(JSON.stringify(prevStampValue));
        newArray[index].outputValue = Number(inputValue * val);
        newArray[index].value = Number(val);
        getSumOfRupees(newArray);
        return newArray;
      });
    } else {
      getSumOfRupees(stampValue);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmitForm)} className="form">
      <div className="p-5 max-h-[500px] overflow-y-auto">
        <table className="striped-table">
          <thead>
            <tr>
              <th>{t('stampValue')}</th>
              <th>{t('count')}</th>
              <th>{t('totalAmount')}</th>
            </tr>
          </thead>
          <tbody>
            {stampValue?.length > 0
              && stampValue?.map((val, i) => (
                <tr key={val?.id}>
                  <td className="text-[#3C4449] text-[14px] flex justify-center font-medium">{val?.cashDenominationValue}</td>
                  <td aria-label="input" className="text-center">
                    <input
                      id={val?.id}
                      type="number"
                      min="0"
                      onKeyUp={(event) => {
                        if (!NUMBER_ONLY.test(event.key)) {
                          event.preventDefault();
                        }
                      }}
                      onKeyDown={(e) => CASH_DECLARATION_CHARACTERRS?.includes(e.key)
                        && e.preventDefault()}
                      onWheel={(e) => e.target.blur()}
                      placeholder={val?.defaultValue}
                      name={val?.cashDenomination}
                      onChange={(e) => handleRupeesChange(
                        i,
                        e.target.value,
                        val?.cashDenominationValue
                      )}
                      value={val?.value}
                    />
                  </td>
                  <td className="text-[#3C4449] text-[14px] flex justify-center font-medium">{val?.outputValue} Rs.</td>
                </tr>
              ))}

          </tbody>
        </table>
      </div>
      <div className="text-[#09327B] text-[14px] font-normal flex justify-end gap-3 pt-3 pb-3 pr-6">{t('grandTotal')} <span className="font-bold">{sumOfStampValue || 0} Rs</span></div>
      <div>
        <div className="flex justify-end p-[18px]">
          <Button variant="primary_outline" size="sm" mr={3} onClick={handleClose}>
            {t('cancel')}
          </Button>
          <Button variant="primary" size="sm" type="submit">
            {t('save')}
          </Button>
        </div>
      </div>
    </form>
  );
};

const mapStateToProps = createStructuredSelector({
  stampDenominationDetails: getStampDenomination

});

const mapDispatchToProps = (dispatch) => ({
  getCashDenominationTypes: (data) => dispatch(actions.getCashDenominationTypes(data)),
  saveStampOpeningDetails: (data) => dispatch(actions.saveStampOpeningDetails(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(OpeningBalanceEntry);
