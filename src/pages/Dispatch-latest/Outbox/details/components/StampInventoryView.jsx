import { t } from 'common/components';
import {
  getStampDenomination, getStampDetails, getStampInventoryViewListParams, getStampInventoyrTableList
} from 'pages/Dispatch-latest/selector';
import React, { useState, useEffect } from 'react';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { actions as commonSliceActions } from 'pages/common/slice';
import { CommonTable } from 'common/components/Table';
import { getTableLoader } from 'pages/common/selectors';
import _ from 'lodash';
import { actions as sliceActions } from '../../../slice';
import * as actions from '../../../actions';
import CountDetails from './CountDetails';
import ButtonDetails from './ButtonDetails';
import ClaimEntryTextField from './ClaimEntryTextField';

const StampInventoryView = ({
  setTableLoader,
  tableLoader, handleClose = () => { },
  handleOpeningBalance = () => { }, stampDenominationDetails,
  getCashDenominationTypes, handleOpenOpeningBalanceViewTable = () => { },
  handleOpenClaimEntryTextbox = () => { },
  handleCloseClaimEntryTextbox = () => { },
  showClaimEntryTextbox,
  handleOpenStampDetails = () => { },
  fetchStampDetails, stampDetails,
  setAmountData,
  claimError,
  amountData,
  setCashInHand = () => { }
}) => {
  const [tableData, setTableData] = useState([]);

  useEffect(() => {
    setTableLoader({ loading: true, id: 'inventory-view-table' });
    fetchStampDetails();
    getCashDenominationTypes();
  }, []);

  useEffect(() => {
    if (stampDetails) {
      setTableLoader({ loading: false, id: 'inventory-view-table' });
      if (Object.keys(stampDetails)?.length > 0) {
        const copyArray = JSON.parse(JSON.stringify(stampDetails));
        const sortedArray = copyArray?.sort((a, b) => {
          return a.stampTypeId - b.stampTypeId;
        });
        setTableData(sortedArray);
      } else {
        setTableData([]);
      }
    }
  }, [stampDetails]);

  useEffect(() => {
    if (tableData?.length > 0) {
      setCashInHand(tableData[0]?.claimBalance || 0);
    }
  }, [tableData]);

  const handleStampTypeId = (fileData) => {
    let stampTypeId;
    if (fileData?.row) {
      const cellData = fileData?.row;
      stampTypeId = (
        <div>{_.filter(stampDenominationDetails, (el) => el?.id === cellData?.stampTypeId)[0]?.cashDenominationValue}</div>
      );
    }
    return <div className="block text-[#3C4449] text-[14px] font-medium">{stampTypeId}</div>;
  };

  const handleCount = (fileData) => {
    let count;
    if (fileData?.row) {
      const cellData = fileData?.row;
      count = (
        <div>{cellData?.count} Nos.</div>
      );
    }
    return <div className="block text-[#3C4449] text-[14px] font-medium">{count}</div>;
  };

  const handleAmount = (fileData) => {
    let amountValue;
    if (fileData?.row) {
      const cellData = fileData?.row;
      amountValue = (
        <div>{cellData?.amount} Rs.</div>
      );
    }
    return <div className="block text-[#3C4449] text-[14px] font-medium">{amountValue}</div>;
  };

  const columns = [

    {
      header: t('stampValue'),
      field: 'stampTypeId',
      alignment: 'left',
      cell: (field) => handleStampTypeId(field)
    },
    {
      header: t('stampCount'),
      field: 'count',
      alignment: 'left',
      cell: (field) => handleCount(field)
    },

    {
      header: t('totalAmount'),
      field: 'amount',
      alignment: 'left',
      cell: (field) => handleAmount(field)
    }

  ];

  const disableClaimAmountButton = () => {
    if (!tableData[0]?.lastClaimAmount && !tableData[0]?.lastRequestedAmount) {
      return false;
    }
    if (tableData[0]?.lastRequestedAmount && !tableData[0]?.lastClaimAmount) {
      return true;
    }
    if (tableData[0]?.lastClaimAmount) {
      return false;
    }
    return false;
  };

  const handleChangeAmount = (e) => {
    const newValue = e.target.value.replace(/[^0-9]/g, '');
    if (newValue <= 0 || newValue === 'e' || newValue === '') {
      setAmountData('');
    } else {
      setAmountData(newValue);
    }
  };

  return (
    <>
      <div>
        <CommonTable
          tableData={tableData}
          columns={columns}
          tableLoader={tableLoader?.loading && tableLoader?.id === 'inventory-view-table'}
        />
      </div>
      <ClaimEntryTextField
        handleChangeAmount={handleChangeAmount}
        showClaimEntryTextbox={showClaimEntryTextbox}
        amountData={amountData}
        handleCloseClaimEntryTextbox={handleCloseClaimEntryTextbox}
        claimError={claimError}
        tableData={tableData}
      />

      <CountDetails
        tableData={tableData}
        handleOpenStampDetails={handleOpenStampDetails}
        stampDetails={stampDetails}
      />

      <ButtonDetails
        handleClose={handleClose}
        tableData={tableData}
        handleOpenOpeningBalanceViewTable={handleOpenOpeningBalanceViewTable}
        handleOpeningBalance={handleOpeningBalance}
        handleOpenClaimEntryTextbox={handleOpenClaimEntryTextbox}
        disableClaimAmountButton={disableClaimAmountButton}
      />
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  inventoryViewParams: getStampInventoryViewListParams,
  tableLoader: getTableLoader,
  stampInventoryList: getStampInventoyrTableList,
  stampDetails: getStampDetails,
  stampDenominationDetails: getStampDenomination
});

const mapDispatchToProps = (dispatch) => ({
  fetchInventoryViewTableList: (data) => dispatch(actions.fetchInventoryViewTableList(data)),
  setStampInventoryViewListParams: (data) => dispatch(sliceActions.setStampInventoryViewListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  fetchStampDetails: (data) => dispatch(actions.fetchStampDetails(data)),
  getCashDenominationTypes: (data) => dispatch(actions.getCashDenominationTypes(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(StampInventoryView);
