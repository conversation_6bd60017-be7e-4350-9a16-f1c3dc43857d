import {
  IconButton
} from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import { t } from 'common/components';
import { BASE_PATH } from 'common/constants';
import { DATE_FORMAT } from 'pages/common/constants';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { dark } from 'utils/color';
import { getDraftSendLetterPreviewList, getDraftSendLetterPreviewListParams, getInboxOutboxCount } from 'pages/Dispatch-latest/selector';
import { getModeOfDispatch, getTableLoader, getUserInfo } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { convertToLocalDate } from 'utils/date';
import TableView from 'assets/TableView';
import * as commonActions from 'pages/common/actions';
import { CommonTable } from 'common/components/Table';
import * as actions from '../../../actions';
import { actions as sliceActions } from '../../../slice';
import DraftUpdateDetails from './DraftUpdateDetails';
import OutBoxFilter from './OutBoxFilter';

const List = ({
  draftSendLetterPreviewListParams, draftSendLetterPreviewList,
  fetchDraftSendLetterPreviewList, setDraftSendLetterPreviewListParams,
  getCashDenominationTypes, fetchModeOfDispatch,
  setTableLoader, tableLoader,
  fecthInboxOutboxCounts, inboxOutboxCount
}) => {
  const activeRows = [{}];
  const [totalItems, setTotalItems] = useState(0);
  const [tableData, setTableData] = useState([]);
  const [numberOfElements, setNumberOfElements] = useState(0);
  const [page, setPage] = useState(0);
  const navigate = useNavigate();
  const [openDraftUpdate, setOpenDraftUpdate] = useState(false);
  const [openEntryForm, setOpenEntryForm] = useState(false);
  const [draftObj, setDratObj] = useState({});
  const [stampDataHide, setStampDataHide] = useState(false);

  useEffect(() => {
    getCashDenominationTypes();
    fetchModeOfDispatch();
  }, []);

  const backToHome = () => {
    navigate(`${BASE_PATH}/services/dispatch`);
  };

  const handleFileNo = (fileData) => {
    let fileNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      fileNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.fileNo}</div>;
    }
    return <div className="block">{fileNo}</div>;
  };

  const handleDraftNo = (fileData) => {
    let draftNo;
    if (fileData?.row) {
      const cellData = fileData?.row;
      draftNo = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.draftNo}</div>;
    }
    return <div className="block">{draftNo}</div>;
  };

  const handleStatus = (fileData) => {
    let dispatchStatus;
    if (fileData?.row) {
      const cellData = fileData?.row;
      const val = cellData?.dispatchStatus
        ? cellData.dispatchStatus.charAt(0).toUpperCase() + cellData.dispatchStatus.slice(1)
        : '';
      dispatchStatus = <div className="text-[14px] font-[600] text-[#D69E2E] max-w-[200px] break-keep">{val}</div>;
    }
    return <div className="block">{dispatchStatus}</div>;
  };

  const handleDraftType = (fileData) => {
    let draftType;
    if (fileData?.row) {
      const cellData = fileData?.row;
      draftType = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.draftTypeInfo}</div>;
    }
    return <div className="block">{draftType}</div>;
  };

  const handleDate = (fileData) => {
    let dispatchedDate;
    if (fileData?.row) {
      const cellData = fileData?.row;
      dispatchedDate = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{convertToLocalDate(cellData?.dispatchDate, DATE_FORMAT.DATE_LOCAL)}</div>;
    }
    return <div className="block">{dispatchedDate}</div>;
  };

  const handleName = (fileData) => {
    let name;
    if (fileData?.row) {
      const cellData = fileData?.row;
      name = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.addressName || cellData?.addressOfficeName || cellData?.copyToName}</div>;
    }
    return <div className="block">{name}</div>;
  };

  const handleSender = (fileData) => {
    let seatName;
    if (fileData?.row) {
      const cellData = fileData?.row;
      seatName = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.seatName}</div>;
    }
    return <div className="block">{seatName}</div>;
  };

  const handleReturnDate = (fileData) => {
    let returnDate;
    if (fileData?.row) {
      const cellData = fileData?.row;
      returnDate = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{convertToLocalDate(cellData?.returnDate, DATE_FORMAT.DATE_LOCAL)}</div>;
    }
    return <div className="block">{returnDate}</div>;
  };

  const handleDispatchedMode = (fileData) => {
    let dispatchedMode;
    if (fileData?.row) {
      const cellData = fileData?.row;
      dispatchedMode = <div className="text-[14px] font-[400] text-[#454545] max-w-[150px] break-keep">{cellData?.modeOfDispatch}</div>;
    }
    return <div className="block">{dispatchedMode}</div>;
  };

  const viewActions = (val) => {
    const stampDataHideValue = [9, 8, 6]?.includes(val?.modeOfDispatchId); // 9:Local delivery, 8:Parcel, 6:Courier
    setStampDataHide(stampDataHideValue);
    setOpenDraftUpdate(true);
    setOpenEntryForm(false);
    setDratObj(val);
  };

  const columns = [
    {
      header: t('fileNo'),
      field: 'fileNo',
      alignment: 'left',
      cell: (field) => handleFileNo(field)
    },
    {
      header: t('draftNo'),
      field: 'draftNo',
      alignment: 'left',
      cell: (field) => handleDraftNo(field)
    },
    {
      header: t('draftType'),
      field: 'draftType',
      alignment: 'left',
      cell: (field) => handleDraftType(field)
    },
    {
      header: t('dispatchedMode'),
      field: 'modeOfDispatchId',
      alignment: 'left',
      cell: (field) => handleDispatchedMode(field)
    },
    {
      header: t('sentDate'),
      field: 'dispatchDate',
      alignment: 'left',
      cell: (field) => handleDate(field)
    },
    {
      header: t('recipient'),
      field: 'name',
      alignment: 'left',
      cell: (field) => handleName(field)
    },
    {
      header: t('sender'),
      field: 'seatName',
      alignment: 'left',
      cell: (field) => handleSender(field)
    },
    {
      header: t('returnDate'),
      field: 'returnDate',
      alignment: 'left',
      cell: (field) => handleReturnDate(field)
    },
    {
      header: t('status'),
      field: 'dispatchStatus',
      alignment: 'left',
      cell: (field) => handleStatus(field)
    },
    {
      header: t('actions'),
      alignment: 'left',
      type: 'actions',
      actions: [
        {
          icon: <TableView />,
          onClick: (row) => {
            viewActions(row);
          }
        }
      ]
    }

  ];

  useEffect(() => {
    if (draftSendLetterPreviewListParams) {
      setTableLoader({ loading: true, id: 'file-table' });
      fetchDraftSendLetterPreviewList();
      fecthInboxOutboxCounts({
        type: 'outbox'
      });
    }
  }, [draftSendLetterPreviewListParams]);

  useEffect(() => {
    if (draftSendLetterPreviewList) {
      setTableLoader({ loading: false, id: 'file-table' });
      if (Object.keys(draftSendLetterPreviewList).length > 0) {
        setTableData(draftSendLetterPreviewList.content);
        setTotalItems(parseInt(`${draftSendLetterPreviewList.totalPages}0`, 10));
        setNumberOfElements(Number(draftSendLetterPreviewList.numberOfElements));
      } else {
        setTableData([]);
        setTotalItems(0);
      }
    }
  }, [draftSendLetterPreviewList]);

  const onPageClick = (data) => {
    setPage(data);
    setDraftSendLetterPreviewListParams({
      ...draftSendLetterPreviewListParams,
      page: data
    });
  };

  return (
    <>
      <div className="flex gap-4 items-center pb-[5px]">
        <div className="flex-none">
          <IconButton onClick={backToHome} variant="unstyled" icon={<BackArrow color={dark} width="12" height="12" />} />
        </div>
        <div className="text-[#09327B] text-[16px] normal font-bold leading-[28px] capitalize grow-[1] pl-[13px]">
          {t('dispatchOutbox')} ({inboxOutboxCount})
        </div>

      </div>
      <div className="bg-white pl-10 py-5 rounded-lg">
        <OutBoxFilter
          setDraftSendLetterPreviewListParams={setDraftSendLetterPreviewListParams}
          draftSendLetterPreviewListParams={draftSendLetterPreviewListParams}
        />
      </div>
      <div className="col-span-12 pb-20 pt-3">
        <CommonTable
          variant="dashboard"
          // tableData={_.filter(tableData, (el) => el?.dispatchStatus !== 'pending')}
          tableData={tableData}
          columns={columns}
          activeRows={activeRows}
          currentPage={page}
          itemsPerPage={10}
          totalItems={totalItems}
          onPageClick={onPageClick}
          paginationEnabled
          tableLoader={tableLoader?.loading && tableLoader?.id === 'file-table'}
          numberOfElements={numberOfElements}
        />

      </div>

      {
        openDraftUpdate && (
          <DraftUpdateDetails
            open={openDraftUpdate}
            type="Dispatch"
            setOpenDraftUpdate={setOpenDraftUpdate}
            setOpenEntryForm={setOpenEntryForm}
            draftObj={draftObj}
            stampDataHide={stampDataHide}
          />
        )
      }
      {
        openEntryForm
        && (
          <DraftUpdateDetails
            open={openEntryForm}
            setOpenEntryForm={setOpenEntryForm}
            type="Update Stamp Details"
            setOpenDraftUpdate={setOpenDraftUpdate}
            draftObj={draftObj}
          />
        )
      }
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  draftSendLetterPreviewListParams: getDraftSendLetterPreviewListParams,
  tableLoader: getTableLoader,
  draftSendLetterPreviewList: getDraftSendLetterPreviewList,
  userInfo: getUserInfo,
  modeOfDispatchDetails: getModeOfDispatch,
  inboxOutboxCount: getInboxOutboxCount
});

const mapDispatchToProps = (dispatch) => ({
  fetchDraftSendLetterPreviewList: (data) => dispatch(actions.fetchDraftSendLetterPreviewList(data)),
  setDraftSendLetterPreviewListParams: (data) => dispatch(sliceActions.setDraftSendLetterPreviewListParams(data)),
  setTableLoader: (data) => dispatch(commonSliceActions.setTableLoader(data)),
  getCashDenominationTypes: (data) => dispatch(actions.getCashDenominationTypes(data)),
  fetchModeOfDispatch: (data) => dispatch(commonActions.fetchModeOfDispatch(data)),
  fecthInboxOutboxCounts: (data) => dispatch(actions.fecthInboxOutboxCounts(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(List);
