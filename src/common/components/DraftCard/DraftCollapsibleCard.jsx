import React, { useMemo } from 'react';
import { primary } from 'utils/color';
import DraftEdit from 'assets/DraftEdit';
import { DRAFT_STATUS, DRAFT_STATUS_CONFIG } from 'pages/common/constants';
import { t } from 'i18next';
import Delete from 'assets/delete';
import { Avatar, CircularProgress } from '@ksmartikm/ui-components';
import { convertToLocalTimeZone } from 'utils/date';
import PrinterBoldIcon from 'assets/PrinterBoldIcon';
import DownloadArrowIcon from 'assets/DownloadArrowIcon';

const IconButton = ({
  icon, onClick, isLoading, isDisabled
}) => {
  return (
    <button
      disabled={isDisabled || isLoading}
      onClick={(e) => {
        e?.stopPropagation();
        onClick(e);
      }}
      className="flex items-center cursor-pointer justify-center w-9 h-9 rounded-lg border border-[#E7EFF5] bg-white shadow-sm hover:bg-gray-100"
    >
      {!isLoading ? icon : <CircularProgress size="15px" isIndeterminate />}
    </button>
  );
};

const DraftCollapsibleCard = (props) => {
  const {
    isDeleteShow = false,
    item = {},
    title = '',
    description = '',
    handleEdit = () => { },
    isEditDisabled = false,
    handlePreview = () => { },
    isEditShow = true,
    correspondence = '',
    fileDetails = {},
    isLoading = { loading: false, id: 'null' },
    handleDraftDelete = () => { },
    from,
    isDeleteCreatedDraft = false,
    handleCreatedDraftDelete = () => { },
    isExpanded,
    onMouseEnter = () => { },
    onMouseLeave = () => { },
    onClick = () => { }
  } = props;

  const formatDate = (date) => {
    return convertToLocalTimeZone(date, 'DD MMM YYYY h:mm A');
  };

  const statusInfo = useMemo(() => DRAFT_STATUS_CONFIG[item.status], [item]);

  const edit = () => {
    return handleEdit(item);
  };

  const handleNavigate = (type) => {
    return handlePreview({ item, type });
  };

  const deleteDraft = () => {
    return handleDraftDelete(item);
  };

  const deleteCreatedDraft = () => {
    return handleCreatedDraftDelete(item);
  };

  const approvedSignatureStatus = (data) => {
    if (item?.status !== DRAFT_STATUS.APPROVED) {
      return null;
    }

    if (data?.isDigitalSigned) {
      return 'and Digitally Signed';
    }

    if (data?.isESigned) {
      return 'and Digitally Signed';
    }

    return '';
  };

  return (
    <div
      className="border border-[#F4F4F4] rounded-[12px] transform transition-all duration-500 ease-in-out overflow-hidden"
      style={{ boxShadow: '0px 4px 10px rgba(0, 0, 0, 0.08)' }}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onClick(e);
        }
      }}
    >
      <div className="py-4 px-5">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Avatar
              w={10}
              h={10}
              fontWeight={600}
              size="14px"
              name={item?.updatedByEmployeeName}
              bg={statusInfo.bgColor}
              color={statusInfo.color}
            />
            <div className="w-full">
              <span className="font-semibold text-[14px] text-[#3C4449]">
                {item?.updatedByEmployeeName}
              </span>
              <p className="text-[#5C6E93] text-[12px] font-semibold">
                {item?.updatedBy}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <p
              className="text-[#5C6E93] text-[14px] font-semibold transition-transform duration-500 ease-in-out"
            >
              {formatDate(item?.date)}
            </p>
          </div>
        </div>

        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out transform ${isExpanded
            ? 'max-h-96 opacity-100 translate-y-0 mt-4'
            : 'max-h-0 opacity-0 -translate-y-4'
          }`}
        >
          {correspondence === 'certificate' ? (
            <h4 className="capitalize font-medium text-sm">
              {fileDetails?.serviceName}
            </h4>
          ) : (
            <div className="flex flex-col gap-3">
              {title && (
                <div
                  className="draft-rich-subject max-w-[100%] line-clamp-1 overflow-hidden"
                  /* eslint-disable-next-line react/no-danger */
                  dangerouslySetInnerHTML={{ __html: title }}
                />
              )}
              {description && (
                <div
                  className="draft-rich-content font-light line-clamp-3 max-h-[98px]"
                  /* eslint-disable-next-line react/no-danger */
                  dangerouslySetInnerHTML={{ __html: description }}
                />
              )}
            </div>
          )}
        </div>

        <div
          className={`overflow-hidden transition-all duration-500 ease-in-out transform ${isExpanded
            ? 'max-h-20 opacity-100 mt-3 border-t pt-2 translate-y-0'
            : 'max-h-0 opacity-0 mt-0 border-t-0 pt-0 -translate-y-4'
          }`}
        >
          <div className="flex justify-between items-center pt-2">
            <div className="flex items-center">
              <div className="flex items-center gap-1">
                {statusInfo?.Icon && (
                  <statusInfo.Icon color={statusInfo?.color} />
                )}
                <span
                  style={{ color: statusInfo?.color }}
                  className="font-medium text-[14px]"
                >
                  {t(statusInfo?.label)} {approvedSignatureStatus(item)}
                </span>
              </div>
              <span className="text-[#5C6E93] text-[14px] font-semibold ml-2 border-l pl-2">
                {correspondence}
              </span>
            </div>

            <div
              className={`flex items-center gap-2 transition-transform duration-300 ease-in-out absolute ${isExpanded
                ? 'opacity-100 scale-100 translate-x-0 relative'
                : 'opacity-0 scale-90 -translate-x-4 pointer-events-none absolute'
              }`}
            >
              {
                (!item?.active) && (
                  <div className="bg-[#E83A7A33] text-[#E83A7A] px-3 py-1 mr-2 rounded-[18px] text-xs font-semibold">
                    {t('inactive')}
                  </div>
                )
              }

              {isEditShow && from !== 'outbox-list' && (
                <IconButton
                  isDisabled={isEditDisabled}
                  onClick={edit}
                  icon={<DraftEdit color={primary} w="24" h="24" />}
                />
              )}

              {isDeleteShow && from !== 'outbox-list' && (
                <IconButton
                  onClick={deleteDraft}
                  icon={<Delete color="#E42E78" />}
                />
              )}

              {isDeleteCreatedDraft && (
                <IconButton
                  onClick={deleteCreatedDraft}
                  icon={<Delete color="#E42E78" />}
                />
              )}

              {item?.status === DRAFT_STATUS.APPROVED && (
                <>
                  <IconButton
                    icon={<PrinterBoldIcon w="19" h="19" stroke="#848484" />}
                    isLoading={
                      isLoading?.loading
                      && isLoading?.id === `${item?.id}-print`
                    }
                    onClick={(e) => handleNavigate('print', e)}
                  />
                  <IconButton
                    icon={<DownloadArrowIcon w="19" h="19" stroke="#848484" />}
                    isLoading={
                      isLoading?.loading
                      && isLoading?.id === `${item?.id}-download`
                    }
                    onClick={(e) => handleNavigate('download', e)}
                  />
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DraftCollapsibleCard;
