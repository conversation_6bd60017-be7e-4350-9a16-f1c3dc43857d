import React from 'react';

import {
  Card, CardBody, IconButton, RichLabel, Button, Tooltip
} from 'common/components';
import ProfilePic from 'assets/ProfilePic';
import CreatedIcon from 'assets/Created';
import VerifiedIcon from 'assets/Verified';
import ApprovedIcon from 'assets/Approved';
import { primary, secondary } from 'utils/color';
import DraftEdit from 'assets/DraftEdit';
import PendingIcon from 'assets/Pending';
import { DRAFT_STATUS } from 'pages/common/constants';
import RejectIcon from 'assets/Rejected';
import ReturnedIcon from 'assets/Returned';
import { t } from 'i18next';
import RecommendIcon from 'assets/RecommendIcon';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import Delete from 'assets/delete';

const DraftCard = (props) => {
  const {
    isDeleteShow = false,
    item = {}, title = '',
    description = '', caption = '', handleEdit = () => { },
    isEditDisabled = false, handlePreview = () => { }, isEditShow = true, correspondence = '', fileDetails = {}, isLoading = { loading: false, id: 'null' }, handleDraftDelete = () => { },
    from, isDeleteCreatedDraft = false, handleCreatedDraftDelete = () => { }
  } = props;

  const edit = () => {
    return handleEdit(item);
  };

  const handleNavigate = (type) => {
    return handlePreview({ item, type });
  };

  const deleteDraft = () => {
    return handleDraftDelete(item);
  };

  const deleteCreatedDraft = () => {
    return handleCreatedDraftDelete(item);
  };

  const profileColor = (color) => {
    switch (color) {
      case DRAFT_STATUS.CREATED:
        return 'rgba(101, 150, 207, 0.3)';
      case DRAFT_STATUS.VERIFIED:
        return 'rgba(207, 135, 187, 0.3)';
      case DRAFT_STATUS.APPROVED:
        return 'rgba(123, 188, 106, 0.3)';
      case DRAFT_STATUS.PENDING:
        return 'rgba(222, 151, 36, 0.3)';
      case DRAFT_STATUS.REJECTED:
        return 'rgba(246, 80, 90, 0.3)';
      case DRAFT_STATUS.RETURNED:
        return 'rgba(129, 132, 198, 0.3)';
      default:
        return '#BCE2F7';
    }
  };

  const profileTextColor = (color) => {
    switch (color) {
      case DRAFT_STATUS.CREATED:
        return 'rgba(101, 150, 207, 1)';
      case DRAFT_STATUS.VERIFIED:
        return 'rgba(207, 135, 187, 1)';
      case DRAFT_STATUS.APPROVED:
        return 'rgba(123, 188, 106, 1)';
      case DRAFT_STATUS.PENDING:
        return 'rgba(222, 151, 36, 1)';
      case DRAFT_STATUS.REJECTED:
        return 'rgba(246, 80, 90, 1)';
      case DRAFT_STATUS.RETURNED:
        return 'rgba(129, 132, 198, 1)';
      default:
        return '#784B8D';
    }
  };

  return (
    <Card className="mt-5" borderRadius="10px" key={item.id}>
      <CardBody>
        <div className={correspondence !== 'certificate' ? 'flex gap-5 items-start' : 'flex gap-5 items-center'}>
          <div className="flex-none">
            <div className="rounded-full w-12 h-12 p-3" style={{ background: profileColor(item.status) }}><ProfilePic color={profileTextColor(item.status)} /></div>
          </div>
          <div className="flex-grow" style={{ width: 'calc(100% - 120px)' }}>
            {correspondence !== 'certificate' ? (
              <>
                <h4 className="font-medium text-medium capitalize">{title}</h4>
                <div className="row-span-2 col-span-12 text-slate-500 relative line-clamp-3 whitespace-pre-wrap">
                  <RichLabel value={description} />
                </div>
              </>
            ) : (
              <h4 className="font-medium text-medium capitalize">{fileDetails?.serviceName}</h4>
            )}
          </div>
          {isEditShow && from !== 'outbox-list'
            && (
              <div className="flex-none text-xs text-right">
                <Tooltip label={t('edit')}>
                  <IconButton variant="ghost" className="bg-none" isDisabled={isEditDisabled} onClick={edit} icon={<DraftEdit width="30px" height="30px" color={primary} />} />
                </Tooltip>
              </div>
            )}
          {isDeleteShow && from !== 'outbox-list'
            && (
              <div className="flex-none text-xs text-right">
                <Tooltip label={t('delete')}>
                  <IconButton variant="ghost" className="bg-none" onClick={deleteDraft} icon={<Delete width="30px" height="30px" color={secondary} />} />
                </Tooltip>
              </div>
            )}
          {isDeleteCreatedDraft
            && (
              <div className="flex-none text-xs text-right">
                <Tooltip label={t('delete')}>
                  <IconButton variant="ghost" className="bg-none" onClick={deleteCreatedDraft} icon={<Delete width="30px" height="30px" color={secondary} />} />
                </Tooltip>
              </div>
            )}
          {item.status === DRAFT_STATUS.APPROVED
            && (
              <div className="flex-none text-xs text-right">
                <Tooltip label={t('print')}>
                  <IconButton variant="ghost" className="bg-none" isLoading={isLoading?.loading && isLoading?.id === `${item?.id}-print`} onClick={() => handleNavigate('print')} icon={<PrintIcon width="30px" height="30px" color={secondary} />} />
                </Tooltip>

                <Tooltip label={t('download')}>
                  <IconButton variant="ghost" className="bg-none" isLoading={isLoading?.loading && isLoading?.id === `${item?.id}-download`} onClick={() => handleNavigate('download')} icon={<DownloadIcon width="30px" height="30px" color={secondary} />} />
                </Tooltip>
              </div>
            )}
        </div>
        <div className="md:flex sm:block mt-5 items-center border-t pt-3 text-xs">
          <div className="flex-none  border-r pr-3">
            {item?.updatedByEmployeeName}
          </div>
          <div className="flex-none pr-3 pl-3">
            {item?.updatedBy}
          </div>
          <div className="flex-none text-xs text-zinc-400 border-l pl-5 xl:pr-3 sm:pr-5">
            {caption}
          </div>
        </div>
        <div className="flex-nowrap items-center xl:flex sm:block">
          <div className="flex-none pr-5">
            {item.status === DRAFT_STATUS.PENDING && <h4 className="flex gap-3" style={{ color: '#DE9724' }}><PendingIcon /> {t('pending')}</h4>}
            {item.status === DRAFT_STATUS.CREATED && <h4 className="flex gap-3" style={{ color: 'rgba(101, 150, 207, 1)' }}><CreatedIcon /> {t('created')}</h4>}
            {item.status === DRAFT_STATUS.VERIFIED && <h4 className="flex gap-3" style={{ color: 'rgba(207, 135, 187, 1)' }}><VerifiedIcon /> {t('verified')}</h4>}
            {item.status === DRAFT_STATUS.APPROVED && <h4 className="flex gap-3" style={{ color: 'rgba(123, 188, 106, 1)' }}><ApprovedIcon /> {t('approved')} {item?.isESigned && 'and Digitaly Signed'}</h4>}
            {item.status === DRAFT_STATUS.RETURNED && <h4 className="flex gap-3" style={{ color: 'rgba(129, 132, 198, 1)' }}><ReturnedIcon /> {t('returned')}</h4>}
            {item.status === DRAFT_STATUS.REJECTED && <h4 className="flex gap-3" style={{ color: 'rgba(246, 80, 90, 1)' }}><RejectIcon /> {t('rejected')}</h4>}
            {item.status === DRAFT_STATUS.RECOMMENDED && <h4 className="flex gap-3" style={{ color: 'rgba(246, 80, 90, 1)' }}><RecommendIcon /> {t('recommended')}</h4>}
          </div>

          <div className="flex-none text-md font-bold text-gray-600 border-l pl-5 xl:pr-3 sm:pr-5 capitalize">
            {correspondence}
          </div>
          <div className="border-l px-3">
            {
              !item?.active && <h1 className=" bg-[#FBE2FF80] px-3 mx-2 text-[#E83A7A] rounded-lg font-medium">{t('inactive')}</h1>
            }
          </div>
          <div className="flex-grow" />
          <div className="flex-none">
            <Button variant="primary_outline" size="xs" isLoading={isLoading?.loading && isLoading?.id === `${item?.id}-preview`} onClick={() => handleNavigate('preview')}>{t('preview')}</Button>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};

export default DraftCard;
