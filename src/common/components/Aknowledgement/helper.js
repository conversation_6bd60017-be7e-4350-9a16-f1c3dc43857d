import { generatePdf } from 'hooks/generatePdf';

const generate = async ({
  downloadUrl = '', setLoading = () => {}, setBaseCode = () => {}, errorTost = () => {}, t = () => {}
}) => {
  const res = generatePdf({ url: downloadUrl });
  const { data, status } = await res.then((result) => result);
  if (status === 'success') {
    setLoading(false);
    setBaseCode(data);
  } else {
    setLoading(false);
    errorTost({
      title: t('errorOnLoading'),
      description: t('pdfLoadIssue')
    });
  }
};

export { generate };
