import React, { useState, useEffect } from 'react';
import {
  Modal, ModalBody, ModalContent, ModalFooter, ModalOverlay, t, Button, Toast
} from 'common/components';
import {
  PdfViewer, Spinner
} from '@ksmartikm/ui-components';

import { secondary } from 'utils/color';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import { generate } from './helper';

const DownloadAck = (props) => {
  const {
    handleClose = () => { }, open, downloadUrl
  } = props;

  const [loading, setLoading] = useState(true);
  const [baseCode, setBaseCode] = useState('');
  const { errorTost } = Toast;

  useEffect(() => {
    if (open) {
      setBaseCode('');
      setLoading(true);
      generate({
        downloadUrl, setLoading, setBaseCode, errorTost, t
      });
    }
  }, [open]);

  const printAck = () => {
    printBlob(baseCode);
  };

  const downloadAck = () => {
    downloadBlob({ blob: baseCode, fileName: 'KSMART-INWARD-ACKNOWLEDGEMENT.pdf' });
  };

  return (
    <Modal size="4xl" isOpen={open}>
      <ModalOverlay />
      <ModalContent style={{ width: '645px' }} className="bg-white">
        <ModalBody py={6}>
          <div style={{ height: 'calc(100vh - 200px)', overflowY: 'auto' }}>
            {loading ? <Spinner style={{ margin: '100px 280px' }} /> : <PdfViewer title="do" height="760px" width="600px" type="application/pdf" data={baseCode} />}
          </div>
        </ModalBody>
        <ModalFooter className="border-t-solid border-t-[#E8ECEE] border-t-[1px]">
          <div className="flex justify-between items-center w-full">
            <div className="flex items-center gap-6" />
            <div className="flex items-center gap-2 cursor-pointer">
              <Button variant="secondary_outline" onClick={printAck} leftIcon={<PrintIcon width="21px" height="21px" color={secondary} />}>{t('print')}</Button>
              <Button variant="secondary_outline" onClick={downloadAck} leftIcon={<DownloadIcon width="21px" height="21px" color={secondary} />}>{t('download')}</Button>
              <Button
                variant="secondary"
                onClick={handleClose}
              >
                {t('close')}
              </Button>
            </div>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default DownloadAck;
