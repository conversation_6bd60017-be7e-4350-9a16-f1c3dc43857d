import React, { useEffect, useState } from 'react';
import '../table.css';
import _ from 'lodash';
import { Checkbox, Toast } from 'common/components';
import Pagination from '../Pagination';
import MoreActions from '../CommonTable/MoreActions';

const InwardTable = ({
  tableData = [],
  columns = [],
  selectedServices = [],
  onRowClick = () => { },
  onRowCheck = () => { },
  activeRows = [],
  itemsPerPage,
  currentPage = 1,
  paginationEnabled,
  onPageClick = () => { },
  totalItems,
  numberOfElements
}) => {
  const [checkedItems, setCheckedItems] = useState([]);
  const [selectedRows, setSelectedRows] = useState(new Set());
  const tempArr = selectedRows;
  const { errorTost } = Toast;
  const currentItems = tableData || [];
  useEffect(() => {
    tempArr.clear();
    const stringifiedActiveRows = activeRows?.map((item) => JSON.stringify(item));
    const activeArr = currentItems?.map((item) => stringifiedActiveRows?.includes(JSON.stringify(item)));

    setCheckedItems([...activeArr]);

    currentItems?.map((item) => {
      if (stringifiedActiveRows?.includes(JSON.stringify(item))) {
        tempArr.add(item);
      }
      return '';
    });
    setSelectedRows(tempArr);
  }, [currentPage, JSON.stringify(activeRows), JSON.stringify(tableData)]);

  const rowValues = (e, row) => {
    if (e.target.checked === true) {
      tempArr.add(row);
    } else if (e.target.checked === false) {
      tempArr.delete(row);
    }

    setSelectedRows(tempArr);
    onRowCheck({ elementCheck: e.target.checked, selectedRow: row, selectedRows: tempArr });

    return tempArr;
  };

  const selectAllValues = (e) => {
    const results = [];
    currentItems.forEach((item) => {
      results.push(rowValues(e, item));
    });
    return results;
  };

  const allChecked = checkedItems.every(Boolean);
  const isIndeterminate = checkedItems.some(Boolean) && !allChecked;

  const checkFun = (e, row, index) => {
    const items = [...checkedItems];
    items[index] = e.target.checked;
    setCheckedItems(items);
    rowValues(e, row);
  };

  const handleIndividualCheck = (e, index, row) => {
    const searchString = row.serviceName || '';
    if (e.target.checked) {
      if (selectedServices?.includes(searchString)) {
        checkFun(e, row, index);
      } else if (selectedServices?.length > 0) {
        errorTost({
          title: 'Different Services',
          description: 'Select the Inwards having same type of Service'
        });
      } else {
        checkFun(e, row, index);
      }
    } else {
      checkFun(e, row, index);
    }
  };

  const handleAllCheck = (e) => {
    if (checkedItems.every(Boolean)) {
      setCheckedItems([...Array(itemsPerPage)].map(() => false));
    } else {
      setCheckedItems([...Array(itemsPerPage)].map(() => true));
    }

    selectAllValues(e);
  };

  return (
    <div className="stripp">
      <table className="striped-table">
        <thead>
          <tr>
            {columns?.map((col) => {
              const currentDate = Date.parse(new Date());
              if (col?.type === 'multi-select') {
                return (
                  <th
                    key={`header-${currentDate + columns.indexOf(col)}`}
                    className={`th-${col?.alignment}`}
                    aria-label="th"
                  >
                    <Checkbox
                      isChecked={allChecked}
                      isIndeterminate={isIndeterminate}
                      onChange={(e) => handleAllCheck(e)}
                    />
                  </th>
                );
              }

              return (
                <th
                  key={`header-${currentDate + columns.indexOf(col)}`}
                  className={`th-${col?.alignment}`}
                  aria-label="th"
                >
                  {col?.header}
                </th>
              );
            })}
          </tr>
        </thead>
        <tbody>
          {currentItems.length > 0 && currentItems?.map((row, index) => {
            const newIndex = `tbodyRow-${index}`;
            return (
              <tr key={newIndex} onClick={() => onRowClick({ row, index })}>
                {columns.length > 0 && columns?.map((col, columnIndex) => {
                  const { cell = ({ field }) => field } = col;
                  const columnKey = `${index - columnIndex}`;
                  if (col?.type === 'multi-select' || col?.type === 'select') {
                    return (
                      <td
                        key={`select-${columnKey}`}
                        className={`th-${col?.alignment}`}
                        aria-label="td"
                      >
                        <Checkbox
                          isChecked={checkedItems[index]}
                          onChange={(e) => handleIndividualCheck(e, index, row)}
                        />
                      </td>
                    );
                  }
                  if (col?.type === 'actions') {
                    const actions = col?.actions;
                    return (
                      <td
                        key={`action-${columnKey}`}
                        className={`th-${col.alignment}`}
                        // onClick={(e) => rowValues(e, row)}
                        aria-label="td"
                      >
                        <MoreActions actionData={actions} rowData={row} />
                      </td>
                    );
                  }
                  return (
                    <td
                      key={`normal-${columnKey}`}
                      className={`th-${col.alignment}`}
                      aria-label="td"
                    >
                      {cell({
                        field: _.get(row, _.get(col, 'field', ''), null),
                        row,
                        rowIndex: index,
                        columnIndex
                      })}
                    </td>
                  );
                })}
              </tr>
            );
          })}

        </tbody>

      </table>
      {paginationEnabled && (
        <Pagination
          className="pagination-bar"
          currentPage={currentPage + 1}
          totalCount={totalItems}
          pageSize={itemsPerPage}
          onPageChange={(page) => onPageClick(page - 1)}
          numberOfElements={numberOfElements}
        />
      )}

    </div>
  );
};

export default InwardTable;
