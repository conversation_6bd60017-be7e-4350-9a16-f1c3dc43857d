import React, { useEffect, useState } from 'react';
import _ from 'lodash';
import NoNotesIcon from 'assets/NoNotesIcon';
import { Checkbox, ResponsivePagination } from 'common/components';
import { Spinner } from '@ksmartikm/ui-components';
import MoreActions from '../CommonTable/MoreActions';
import Pagination from '../Pagination';

const getExistingItems = (currentActiveRows, currentPageItems) => {
  const tempSet = new Set();
  const stringifiedActiveRows = currentActiveRows?.map((item) => JSON.stringify(item));
  const activeArr = currentPageItems?.map((item) => stringifiedActiveRows.includes(JSON.stringify(item)));

  _.forEach(currentPageItems, (item) => {
    if (stringifiedActiveRows.includes(JSON.stringify(item))) {
      tempSet.add(item);
    }
  });
  return { checkedItems: activeArr, selectedRows: tempSet };
};

const NoDataDisplay = ({ noDataText }) => (
  <div className="p-10 text-center bg-white rounded-lg">

    <NoNotesIcon width="100px" height="100px" className="mx-auto" />
    <h4 className="mt-3 text-gray-600 font-medium">{noDataText}</h4>
  </div>
);

const ResponsiveTable = ({
  tableData = [],
  columns = [],
  onRowClick = () => { },
  onRowCheck = () => { },
  onRowRadioSelected = () => { },
  activeRows = [],
  itemsPerPage,
  currentPage = 1,
  paginationEnabled,
  onPageClick = () => { },
  totalItems,
  variant = 'normal',
  onRowCheckEnabled,
  tableLoader = false,
  noDataText = 'No Records Found',
  numberOfElements,
  className
}) => {
  const [checkedItems, setCheckedItems] = useState([]);
  const [selectedRows, setSelectedRows] = useState(new Set());
  const tempArr = selectedRows;
  const currentItems = tableData || [];

  useEffect(() => {
    tempArr.clear();
    const {
      checkedItems: currentCheckedItems,
      selectedRows: currentSelectedRows
    } = getExistingItems(activeRows, currentItems);

    setCheckedItems([...currentCheckedItems]);
    setSelectedRows(currentSelectedRows);
  }, [currentPage, JSON.stringify(activeRows), JSON.stringify(tableData)]);

  const rowValues = (e, row) => {
    if (e.target.checked === true) {
      tempArr.add(row);
    } else if (e.target.checked === false) {
      tempArr.delete(row);
    }

    setSelectedRows(tempArr);
    onRowCheck({
      elementCheck: e.target.checked,
      selectedRow: row,
      selectedRows: tempArr
    });

    return tempArr;
  };

  const selectAllValues = (e) => {
    const results = [];
    currentItems.forEach((item) => {
      results.push(rowValues(e, item));
    });
    return results;
  };

  const allChecked = checkedItems.every(Boolean);
  const isIndeterminate = checkedItems.some(Boolean) && !allChecked;

  const handleIndividualCheck = (e, index, row) => {
    const items = [...checkedItems];
    items[index] = e.target.checked;
    setCheckedItems(items);
    rowValues(e, row);
  };

  const handleAllCheck = (e) => {
    if (checkedItems.every(Boolean)) {
      setCheckedItems([...Array(itemsPerPage)].map(() => false));
    } else {
      setCheckedItems([...Array(itemsPerPage)].map(() => true));
    }
    selectAllValues(e);
  };

  const radioSelected = (row) => {
    const convertSetToArr = [...selectedRows];
    const stringifiedActiveRow = JSON.stringify(convertSetToArr[0]);
    const stringifiedRow = JSON.stringify(row);
    return stringifiedActiveRow === stringifiedRow;
  };

  const handleRadioSelect = (row) => {
    setSelectedRows(new Set([row]));
    onRowRadioSelected(row);
  };

  const getTextAlignmentClass = (alignment) => {
    if (alignment === 'center') return 'text-center';
    if (alignment === 'right') return 'text-right';
    return 'text-left';
  };

  const renderCellContent = (col, columnKey, row, index, columnIndex) => {
    const { cell = ({ field }) => field } = col;
    const alignmentClass = getTextAlignmentClass(col.alignment);

    if (col?.type === 'multi-select' || col?.type === 'select') {
      return (
        <td key={`select-${columnKey}`} className={`p-3 ${alignmentClass}`}>
          <Checkbox
            checked={checkedItems[index]}
            onCheckedChange={(checked) => handleIndividualCheck({ target: { checked } }, index, row)}
          />
        </td>
      );
    }

    if (col?.type === 'radio') {
      return (
        // eslint-disable-next-line jsx-a11y/control-has-associated-label
        <td key={`radio-${columnKey}`} className={`p-3 ${alignmentClass}`}>
          <input
            type="radio"
            value={index}
            checked={radioSelected(row)}
            onChange={() => handleRadioSelect(row)}
            className="h-4 w-4"
          />
        </td>
      );
    }

    if (col?.type === 'actions') {
      const actions = col?.actions;
      return (
        <td key={`action-${columnKey}`} className={`p-3 ${alignmentClass}`}>
          <MoreActions actionData={actions} rowData={row} />
        </td>
      );
    }

    let responsiveClass = '';
    if (col.responsive === false) {
      responsiveClass = 'hidden md:table-cell';
    }

    return (
      <td
        key={`normal-${columnKey}`}
        className={`p-3 ${alignmentClass} ${responsiveClass}`}
        data-label={col.header}
      >
        {cell({
          field: _.get(row, _.get(col, 'field', ''), null),
          row,
          rowIndex: index,
          columnIndex
        })}
      </td>
    );
  };

  const renderRow = (row, index) => {
    const cursorClass = onRowCheckEnabled ? 'cursor-pointer' : '';

    return (
      <tr
        key={`tbodyRow-${index}`}
        onClick={() => onRowClick({ row, index })}
        className={`${cursorClass} border-b hover:bg-gray-50`}
      >
        {columns?.map((col, columnIndex) => {
          const columnKey = `${index}-${columnIndex}`;
          return renderCellContent(col, columnKey, row, index, columnIndex);
        })}
      </tr>
    );
  };

  const renderEmptyTableBody = () => (
    <tbody>
      <tr>
        <td colSpan={columns?.length} className="p-0">
          <NoDataDisplay noDataText={noDataText} />
        </td>
      </tr>
    </tbody>
  );

  const renderTableItems = () => (
    <tbody>
      {currentItems.map((row, index) => renderRow(row, index))}
    </tbody>
  );

  const renderTableData = () => {
    if (currentItems.length === 0) {
      return renderEmptyTableBody();
    }
    return renderTableItems();
  };

  const renderHeaderCell = (col, index) => {
    if (col?.type === 'multi-select') {
      const alignmentClass = getTextAlignmentClass(col.alignment);

      return (
        <th
          key={`header-${index}`}
          className={`px-3 py-3 text-xs font-medium uppercase tracking-wider ${alignmentClass}`}
        >
          <Checkbox
            aria-label="Select all rows"
            checked={allChecked}
            ref={(input) => {
              if (input) {
                // eslint-disable-next-line no-param-reassign
                input.indeterminate = isIndeterminate;
              }
            }}
            onCheckedChange={(checked) => handleAllCheck({ target: { checked } })}
          />
        </th>
      );
    }

    const alignmentClass = getTextAlignmentClass(col.alignment);
    let responsiveClass = '';
    if (col.responsive === false) {
      responsiveClass = 'hidden md:table-cell';
    }

    return (
      <th
        key={`header-${index}`}
        className={`px-3 py-3 text-xs font-medium uppercase tracking-wider ${alignmentClass} ${responsiveClass}`}
      >
        {col?.header}
      </th>
    );
  };

  const renderTableLoader = () => (
    <tbody>
      <tr>
        <td colSpan={columns?.length}>
          <div className="flex justify-center items-center h-64">
            <Spinner className="h-8 w-8" />
          </div>
        </td>
      </tr>
    </tbody>
  );

  const renderCardViewItem = (row, rowIndex) => {
    return (
      // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
      <div
        key={`card-${rowIndex}`}
        className="bg-white rounded-lg shadow p-4"
        onClick={() => onRowCheckEnabled && onRowClick({ row, index: rowIndex })}
      >
        {columns.map((col, colIndex) => {
          if (col?.type === 'multi-select' || col?.type === 'select') {
            return (
              // eslint-disable-next-line react/no-array-index-key
              <div key={`card-select-${rowIndex}-${colIndex}`} className="flex justify-end mb-2">
                <Checkbox
                  checked={checkedItems[rowIndex]}
                  onCheckedChange={(checked) => handleIndividualCheck({ target: { checked } }, rowIndex, row)}
                />
              </div>
            );
          }

          if (col?.type === 'radio') {
            return (
              // eslint-disable-next-line react/no-array-index-key
              <div key={`card-radio-${rowIndex}-${colIndex}`} className="flex justify-end mb-2">
                <input
                  type="radio"
                  checked={radioSelected(row)}
                  onChange={() => handleRadioSelect(row)}
                  className="h-4 w-4"
                />
              </div>
            );
          }

          if (col?.type === 'actions') {
            return (
              // eslint-disable-next-line react/no-array-index-key
              <div key={`card-action-${rowIndex}-${colIndex}`} className="flex justify-end mb-2">
                <MoreActions actionData={col?.actions} rowData={row} />
              </div>
            );
          }

          if (col.header) {
            const { cell = ({ field }) => field } = col;
            const value = cell({
              field: _.get(row, _.get(col, 'field', ''), null),
              row,
              rowIndex,
              columnIndex: colIndex
            });

            return (
              // eslint-disable-next-line react/no-array-index-key
              <div key={`card-item-${rowIndex}-${colIndex}`} className="py-2 flex justify-between border-b last:border-0">
                <span className="font-medium text-gray-600">{col.header}</span>
                <span>{value}</span>
              </div>
            );
          }

          return null;
        })}
      </div>
    );
  };

  const renderEmptyCardView = () => (
    <NoDataDisplay noDataText={noDataText} />
  );

  const renderCardItems = () => (
    currentItems.map((row, rowIndex) => renderCardViewItem(row, rowIndex))
  );

  const renderResponsiveCards = () => {
    return (
      <div className="block md:hidden space-y-4 max-w-full">
        {currentItems.length === 0 ? renderEmptyCardView() : renderCardItems()}
      </div>
    );
  };

  const renderMobileLoader = () => (
    <div className="md:hidden flex justify-center items-center h-64">
      <Spinner className="h-8 w-8" />
    </div>
  );

  return (
    <>
      {/* Table for medium screens and up */}
      <div className={`hidden md:block rounded-lg border ${variant === 'normal' ? '' : 'stripp'}`}>
        <table className={`min-w-full divide-y divide-gray-200 striped-table ${className || ''}`}>
          <thead className="bg-gray-50">
            <tr>
              {columns?.map((col, index) => renderHeaderCell(col, index))}
            </tr>
          </thead>
          {tableLoader ? renderTableLoader() : renderTableData()}
        </table>
      </div>

      {/* Card view for small screens */}
      {tableLoader ? renderMobileLoader() : renderResponsiveCards()}

      {/* Pagination */}
      {paginationEnabled && (
        <>
          <div className="block md:hidden">
            <ResponsivePagination
              currentPage={currentPage + 1}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onPageChange={(page) => onPageClick(page - 1)}
              numberOfElements={numberOfElements}
            />
          </div>
          <div className="hidden md:block">
            <Pagination
              currentPage={currentPage + 1}
              totalCount={totalItems}
              pageSize={itemsPerPage}
              onPageChange={(page) => onPageClick(page - 1)}
              numberOfElements={numberOfElements}
            />
          </div>
        </>
      )}
    </>
  );
};

export default ResponsiveTable;
