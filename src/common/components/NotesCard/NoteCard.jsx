import ProfilePic from 'assets/ProfilePic';
import { NOTE_CREATED_BY } from 'pages/common/constants';
import React from 'react';
import { RichLabel } from 'common/components';
import { convertToLocalTimeZone } from 'utils/date';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { actions as sliceActions, actions as commonSliceActions } from 'pages/common/slice';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { BASE_PATH, STORAGE_KEYS } from 'common/constants';
import { actions as fileSliceActions } from 'pages/file/details/slice';
import { API_URL } from 'common/urls';
import { baseApiURL } from 'utils/http';
import { getNoteCardDetails, getUserInfo } from 'pages/common/selectors';

export const profileColor = (color) => {
  switch (color) {
    case NOTE_CREATED_BY.OPERATOR:
      return '#DDF0FA';
    case NOTE_CREATED_BY.VERIFIER:
      return '#FBE2FF';
    case NOTE_CREATED_BY.APPROVER:
      return '#CBF2C1';
    case NOTE_CREATED_BY.RECOMMENDING_OFFICER:
      return '#FEEDC8';
    default:
      return '#BCE2F7';
  }
};

export const profileTextColor = (color) => {
  switch (color) {
    case NOTE_CREATED_BY.OPERATOR:
      return '#00B2EB';
    case NOTE_CREATED_BY.VERIFIER:
      return '#CF87BB';
    case NOTE_CREATED_BY.APPROVER:
      return '#7BBC6A';
    case NOTE_CREATED_BY.RECOMMENDING_OFFICER:
      return '#FBBA23';
    default:
      return '#6596CF';
  }
};

const NoteCard = ({
  item = {},
  attachments,
  isOnSelectNote = null,
  setIsOnSelectNote = () => {},
  from = '',
  draftDetails,
  noteReferences,
  setNoteCardDetails,
  setDocumentId,
  index = 0,
  setIsOneDocumentSelect,
  setShowingAllDocs,
  lastNote = null,
  setDraftFilterActiveIndex = () => {}
}) => {
  const navigate = useNavigate();
  const params = useParams();
  const location = useLocation();
  const formatedNote = item?.notes?.replace(/^\s*<br\s*(\/)?>/i, '');
  const formatedNote2 = formatedNote?.replace('<p style="line-height: 2"><br>', '<p style="line-height: 2">');

  const handleElse = () => {
    navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=0`);
    setNoteCardDetails(item?.notesDocsDetails);
    setIsOnSelectNote(isOnSelectNote === item?.notesId ? null : item?.notesId);
    setShowingAllDocs(false);
    if (item?.notesDocsDetails?.length > 0 && isOnSelectNote !== item.notesId) {
      const docId = item.notesDocsDetails[0].content?.notesDocumentId || item.notesDocsDetails[0].content?.fileId;
      const fromType = item.notesDocsDetails[0].content?.notesDocumentId ? 'note' : 'inward';

      setIsOneDocumentSelect({ docId, noteNo: item.noteNo });
      setDocumentId({ docId, from: fromType });
    } else {
      setIsOneDocumentSelect({});
      setDocumentId(null);
    }
  };

  const handleNotesClick = async (event) => {
    if (!location.pathname.includes('summary') && from !== 'create-draft') {
      event.stopPropagation();
      if (lastNote === item?.noteNo && item?.draftDetails?.length > 0) {
        const draftExistResponse = await fetch(
          `${baseApiURL}/${API_URL.INBOX.DRAFT_EXISTS_OR_NOT.replace(':fileNo', params?.fileNo)}`,
          {
            method: 'GET',
            headers: {
              Authorization: `Bearer ${localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)}`
            }
          }
        );
        const existRes = await draftExistResponse.json();
        if (existRes?.payload === true) {
          setDraftFilterActiveIndex(1);
          setIsOnSelectNote(isOnSelectNote === item?.notesId ? null : item?.notesId);
          navigate(`${BASE_PATH}/file/${params?.fileNo}/notes?show=1`);
          const documnetId = item?.notesDocsDetails[0]?.content?.notesDocumentId || item?.notesDocsDetails[0]?.content?.fileId || null;
          setIsOneDocumentSelect({ docId: documnetId, noteNo: item.noteNo });
          setNoteCardDetails(item?.notesDocsDetails || []);
          const fromType = item.notesDocsDetails[0].content?.notesDocumentId ? 'note' : 'inward';
          setDocumentId({ docId: documnetId, from: fromType });
        } else {
          handleElse();
        }
      } else {
        handleElse();
      }
    }
  };

  return (
    <div
      key={item.notesId}
      id={`note-card-${item?.noteNo}`}
      aria-hidden
      className={`${index !== 0 && 'mt-5'} p-5 rounded-xl bg-white relative ${from === 'note' && 'cursor-pointer'}`}
      style={{
        border: isOnSelectNote === item.notesId && from === 'note' ? '1.9px solid #868585' : '1px solid #C9D1D7'
      }}
      onClick={(e) => {
        handleNotesClick(e);
      }}
    >
      <div id={`summary-note-card-${item?.noteNo}`} className="absolute mt-[-230px]" />

      <div className="flex gap-5 items-center">
        <div className="flex-none">
          <div className="rounded-full w-12 h-12 p-3" style={{ background: profileColor(item.role) }}>
            <ProfilePic color={profileTextColor(item.role)} />
          </div>
        </div>
        <div className="flex-grow">
          <div className="flex items-center">
            <div className="flex-grow">
              <h4 className="font-semibold text-[14px]">
                {item?.assignerName} , {item?.designation}
                {item?.seatName ? <span>, {item?.seatName}</span> : null}
                {item?.postName ? <span>, {item?.postName}</span> : null}
              </h4>
            </div>
            <div className="flex-none">
              <span className="ml-auto text-xs pr-5 text-[#5C6E93] text-[16px] font-normal">
                {convertToLocalTimeZone(item?.updatedAt)}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className="flex gap-5">
        <div className="flex-none w-12 text-center pt-[10px] text-[#00B2EC] text-[14px] font-semibold">
          {item?.noteNo || 0}
        </div>
        <div className="flex-grow text-[#323232] text-[14px] font-semibol pt-[5px]">
          <RichLabel value={formatedNote2} className="whitespace-pre-wrap" />
          <div className="flex flex-wrap items-center gap-2">{noteReferences}</div>
          <div className="flex flex-wrap justify-start gap-3 items-end">
            {attachments} {draftDetails}{' '}
          </div>
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  noteCardDetails: getNoteCardDetails,
  userInfo: getUserInfo
});

const mapDispatchToProps = (dispatch) => ({
  setNoteCardDetails: (data) => dispatch(sliceActions.setNoteCardDetails(data)),
  setDocumentId: (data) => dispatch(sliceActions.setDocumentId(data)),
  setIsOneDocumentSelect: (data) => dispatch(commonSliceActions.setIsOneDocumentSelect(data)),
  setShowingAllDocs: (data) => dispatch(fileSliceActions.setShowingAllDocs(data)),
  setDraftFilterActiveIndex: (data) => dispatch(fileSliceActions.setDraftFilterActiveIndex(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(NoteCard);
