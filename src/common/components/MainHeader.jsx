import {
  Box,
  IconButton, Tooltip
} from '@ksmartikm/ui-components';
import { getCommonConfigSelector, getUserInfo, getUserOffices } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import * as commonActions from 'pages/common/actions';
import React, { useEffect } from 'react';

import { connect, useDispatch } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import { STORAGE_KEYS, USER_TYPE } from 'common/constants';
import { getDataFromStorage } from 'utils/encryption';
import { useLocation } from 'react-router-dom';
// import { useForm } from 'react-hook-form';
import UserRoundFilledIcon from 'assets/UserRoundFilledIcon';
import { routeRedirect } from 'utils/common';
// import { FormController } from '.';
import TruncatedText from './TruncatedText';

const Logo = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="85" height="44" viewBox="0 0 100 44" fill="none">
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M32.2117 10.5587C29.5209 10.5587 26.2977 11.4556 26.2977 14.8189C26.2977 16.6968 26.9984 17.7339 28.596 18.827L31.5389 20.845C32.2117 21.3214 32.4639 21.7419 32.4639 22.1904C32.4639 23.5077 31.4269 23.7599 30.0535 23.7599C29.2127 23.7599 26.8863 23.6197 25.569 23.4516C25.0925 23.3956 24.9524 23.5918 24.8963 23.9L24.588 25.5817C24.532 25.9461 24.6441 26.1143 25.0084 26.2264C26.7181 26.7028 29.1286 26.7869 30.0535 26.7869C33.3888 26.7869 36.2477 25.3014 36.2477 21.798C36.2477 20.5647 35.8553 19.4155 34.1736 18.2664L30.8944 16.0242C30.1656 15.5196 30.0535 15.2114 30.0535 14.7348C30.0535 13.6978 31.1746 13.5857 32.2117 13.5857C33.8093 13.5857 35.8833 13.81 36.64 13.922C37.0885 13.9781 37.2287 13.7819 37.2847 13.3895L37.565 11.7919C37.6491 11.3154 37.4809 11.2314 37.1726 11.1473C35.6311 10.7549 33.8934 10.5587 32.2117 10.5587ZM60.6322 14.6508C60.6322 12.1283 59.4269 10.5587 56.7643 10.5587C54.8304 10.5587 53.2607 11.0071 51.8314 11.9321C51.3549 11.2033 50.43 10.5587 48.8324 10.5587C47.1786 10.5587 45.9735 11.1473 44.6842 11.904L44.7963 11.3154C44.8524 11.0071 44.6842 10.7549 44.3198 10.7549H41.8813C41.5731 10.7549 41.2928 11.0071 41.2367 11.3154L38.6582 25.9742C38.6021 26.3385 38.7703 26.5627 39.1066 26.5627H41.6852C42.1897 26.5627 42.3018 26.2824 42.3578 25.9742L44.3479 14.6508C45.6091 14.0341 46.506 13.7259 47.0946 13.7259C48.2437 13.7259 48.6922 14.0341 48.6922 15.0712C48.6922 15.4356 48.6642 15.884 48.5521 16.4446L46.8704 25.9742C46.7863 26.3946 46.9825 26.5627 47.3188 26.5627H49.8974C50.4019 26.5627 50.5141 26.2824 50.5701 25.9742L52.2518 16.4446C52.3639 15.7439 52.4199 15.0152 52.3919 14.4266C53.5131 13.8941 54.6902 13.7259 55.2227 13.7259C56.4279 13.7259 56.9044 14.0622 56.9044 15.1553C56.9044 15.4917 56.8484 15.912 56.7643 16.4446L55.0826 25.9742C55.0265 26.3385 55.1947 26.5627 55.5311 26.5627H58.1096C58.6421 26.5627 58.7262 26.2824 58.7823 25.9742L60.464 16.4446C60.5761 15.7999 60.6322 15.2114 60.6322 14.6508ZM71.1146 10.5587C66.6021 10.5587 63.8834 13.2213 63.1547 17.5938L62.8183 19.5837C62.7343 20.1443 62.6222 21.0132 62.6222 21.826C62.6222 25.1613 64.6121 26.7869 67.1907 26.7869C68.564 26.7869 69.9655 26.3946 71.5351 25.1894L71.423 26.0021C71.3669 26.3105 71.5351 26.5627 71.8714 26.5627H74.1136C74.422 26.5627 74.7302 26.3105 74.7863 26.0021L77.2247 12.0442C77.3088 11.5396 77.1126 11.4276 76.6081 11.2874C74.9545 10.8389 72.9084 10.5587 71.1146 10.5587ZM73.2168 13.7819L71.6752 22.5827C70.4139 23.3115 69.2368 23.7599 68.1717 23.7599C67.0786 23.7599 66.2657 23.3395 66.2657 21.7419C66.2657 21.4056 66.2657 20.901 66.434 19.9481L66.8544 17.5938C67.2467 15.2954 68.1717 13.5857 70.5822 13.5857C71.5911 13.5857 72.7122 13.6698 73.2168 13.7819ZM88.8845 11.0071C88.8845 10.7549 88.8004 10.5587 88.436 10.5587C87.2589 10.5587 85.7734 10.6708 84.1758 11.904L84.2878 11.3154C84.3719 10.895 84.2038 10.7549 83.8114 10.7549H81.3729C80.9525 10.7549 80.7843 11.0071 80.7283 11.3154L78.1497 25.9742C78.0656 26.3946 78.2619 26.5627 78.5982 26.5627H81.1768C81.7093 26.5627 81.7934 26.2824 81.8494 25.9742L83.8395 14.7069C85.7453 13.7539 86.9786 13.6978 87.8755 13.6978C88.3239 13.6978 88.4641 13.4456 88.5201 13.1373L88.8845 11.1192C88.8845 11.0913 88.8845 11.0632 88.8845 11.0071ZM94.3219 7.89603C94.0136 7.95206 93.7334 8.14828 93.6773 8.45655L93.2569 10.7549H90.6223C90.1738 10.7549 90.0336 11.0071 89.9776 11.3154L89.7534 12.5767C89.6973 12.885 89.8094 13.0812 90.2018 13.1373L92.7804 13.5297L91.2389 22.3305C91.1548 22.835 91.0987 23.2834 91.0987 23.7038C91.0987 25.9461 92.4441 26.7869 94.9105 26.7869C95.5271 26.7869 96.1438 26.7028 96.8445 26.5908C97.2088 26.5346 97.405 26.3665 97.4611 26.0862L97.7694 24.3204C97.8254 23.9281 97.5452 23.9 97.2649 23.9H95.7794C95.0787 23.9 94.8264 23.816 94.8264 23.3115C94.8264 23.0872 94.8825 22.7509 94.9666 22.3305L96.5081 13.5297H99.0306C99.4791 13.5297 99.6472 13.3335 99.7033 12.9691L99.9836 11.3154C100.068 10.8389 99.8155 10.7549 99.5071 10.7549H96.9846L97.5172 7.86799C97.5732 7.61574 97.405 7.39147 97.0126 7.44758L94.3219 7.89603Z"
      fill="#09327B"
    />
    <path
      d="M7.74092 15.9409C12.6947 10.9872 17.6486 6.03308 22.6025 1.07924C24.0407 -0.359017 26.3993 -0.360474 27.8389 1.07924L30.5225 3.76281L27.5108 6.81215L25.2208 4.5222L11.1838 18.5591L24.6911 32.0664L21.6605 35.097L7.74092 21.1774C6.30258 19.7391 6.30123 17.3806 7.74092 15.9409Z"
      fill="#E83A7A"
    />
    <path
      d="M7.82153 8.78992C7.90562 8.34147 7.7094 8.25738 7.37309 8.25738H4.13071C3.65428 8.25738 3.51409 8.48164 3.45806 8.78992L0.0106127 28.5217C-0.0454121 28.8581 0.122745 29.0823 0.459059 29.0823H3.70135C4.0097 29.0823 4.28999 28.9982 4.37408 28.5217C6.12559 18.4955 7.77463 9.05737 7.82153 8.78992Z"
      fill="#E83A7A"
    />
    <path
      d="M34.2864 42.4862L44.5359 32.2366C45.3728 31.3997 45.3728 30.043 44.5359 29.2061C43.6991 28.3692 42.3423 28.3692 41.5054 29.2061L31.6681 39.0433L26.3103 33.6855L23.2797 36.716L29.0499 42.4862C30.4882 43.9245 32.8467 43.9259 34.2864 42.4862Z"
      fill="#00B2EB"
    />
  </svg>
);

const Logout = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
    <path d="M9 9.7832L19 9.7832" stroke="#232F50" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M15.5 13.2832L19 9.7832L15.5 6.2832" stroke="#232F50" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10 18.7832C5.02944 18.7832 1 14.7538 1 9.7832C1 4.81264 5.02944 0.783203 10 0.783203" stroke="#232F50" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

const MainHeader = (props) => {
  const {
    setUserInfo,
    fetchLocalBodyByOfficeCode
  } = props;
  const dispatch = useDispatch();
  const location = useLocation();

  // const { control, setValue } = useForm({
  //   mode: 'all',
  //   defaultValues: {
  //     offices: ''
  //   }
  // });

  const userRoles = getDataFromStorage(STORAGE_KEYS.USER_ROLES, true) || [];
  const userDetails = getDataFromStorage(STORAGE_KEYS.USER_DETAILS, true) || [];

  const handleLogout = () => {
    dispatch(commonActions.logout());
  };

  // const handleOffice = (data) => {
  //   localStorage.setItem(STORAGE_KEYS.OFFICE_ID, data?.id);
  //   window.location.reload();
  // };

  const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
  const officeId = JSON.parse(localStorage.getItem(STORAGE_KEYS.OFFICE_ID));
  useEffect(() => {
    if (
      officeId
      && userDetails?.user_type !== USER_TYPE.CITIZEN
    ) {
      const info = {
        userRoles,
        userDetails,
        officeId,
        id: officeId,
        assigner: userDetails?.userId,
        postIds: null
      };
      setUserInfo(info);
      fetchLocalBodyByOfficeCode(officeId);
    } else {
      const info = {
        userRoles,
        userDetails
      };
      setUserInfo(info);
    }
  }, [officeId, token]);

  // useEffect(() => {
  //   if (JSON.parse(localStorage.getItem(STORAGE_KEYS.OFFICE_ID))) {
  //     setValue('offices', Number(localStorage.getItem(STORAGE_KEYS.OFFICE_ID)));
  //   }
  // }, [JSON.parse(localStorage.getItem(STORAGE_KEYS.OFFICE_ID))]);

  return (
    <div className="flex justify-between items-center h-[70px] px-[30px] bg-white border border-b-[#DFE5F3] fixed w-full z-40 top-0">
      <div className="flex items-center gap-x-5">
        <div className="flex-grow">
          <Logo />
        </div>
        {/* {userDetails?.offices?.length > 0 && (
          <div className="min-w-60 max-w-60">
            <FormController
              isClearable={false}
              name="offices"
              type="select"
              className="font-bold text-[12px]"
              control={control}
              options={userDetails?.offices}
              handleChange={(data) => {
                handleOffice(data);
              }}
              optionKey="id"
            />
          </div>
        )} */}

        {/* {
          !location.pathname.includes('public') && !location.pathname.includes('kswift/login') && (
            <div className="font-[700] text-[16px] text-[rgba(9,_50,_123,_0.3)] flex-none pt-[7px]">
              <span>{userDetails?.name} - {userDetails?.designation}</span>
            </div>
          )
        } */}

      </div>

      <div className="flex justify-end gap-x-4">

        {!userDetails?.isAuditor
          && (userDetails?.aadhaarId?.photo?.photo ? (
            <Tooltip label="Profile" aria-label="Profile">
              <img
                aria-hidden
                className="object-fill w-10 h-10 rounded-lg cursor-pointer"
                src={`data:image/png;base64,${userDetails?.aadhaarId?.photo?.photo}`}
                alt="profile"
                onClick={() => routeRedirect(
                  `ui/home/<USER>/my-profile`
                )}
              />
            </Tooltip>
          ) : (
            <Box
              className="flex flex-row gap-2 items-center"
              onClick={() => routeRedirect(
                `ui/home/<USER>/my-profile`
              )}
            >
              <IconButton variant="unstyled" icon={<UserRoundFilledIcon />} />
              <div className="flex flex-col gap-0 !max-w-48">
                <TruncatedText
                  text={userDetails?.name || userDetails?.userName}
                  fontSize={14}
                  fontWeight={700}
                  color="#232F50"
                />
                <TruncatedText
                  maxWidth={['100%', '150px', '250px']}
                  fontWeight={500}
                  text={userDetails?.designation}
                  fontSize={13}
                  color="#5C6E93"
                  mt={-1}
                />
              </div>
            </Box>
          ))}

        {

          !location.pathname.includes('public') && !location.pathname.includes('kswift/login') && (
            <Tooltip label="Logout" aria-label="Logout">
              <IconButton icon={<Logout />} onClick={handleLogout} />
            </Tooltip>
          )
        }

      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  userOffices: getUserOffices,
  userInfo: getUserInfo,
  commonConfig: getCommonConfigSelector
});

const mapDispatchToProps = (dispatch) => ({
  setUserInfo: (data) => dispatch(commonSliceActions.setUserInfo(data)),
  fetchLocalBodyByOfficeCode: (data) => dispatch(commonActions.fetchLocalBodyByOfficeCode(data)),
  setLocale: (data) => dispatch(commonSliceActions.setLocale(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(MainHeader);
