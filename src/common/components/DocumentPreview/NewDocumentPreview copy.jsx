import React, {
  useState, useEffect, useCallback
} from 'react';
import {
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalBody,
  t,
  Toolt<PERSON>,
  ModalHeader
} from 'common/components';
import Forward from 'assets/Forward';
import Backward from 'assets/Backward';
import { dark } from 'utils/color';
import { Spinner } from '@ksmartikm/ui-components';
import { baseApiURL } from 'utils/http';
import NoNotesIcon from 'assets/NoNotesIcon';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import Rotate from 'assets/Rotate';
import FullScreenIcon from 'assets/FullScreen';
import CloseOutlineIcon from 'assets/CloseOutline';
import MiniScreenIcon from 'assets/MiniScreen';
import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  getDocumentId,
  getDocumentNameFromNoteReferences
} from 'pages/common/selectors';
import { getActiveDocsFilterIndex, getNotes } from 'pages/file/details/selector';
import { handleContentType } from 'utils/common';
import ZoomIn from 'assets/ZoomIn';
import ZoomOut from 'assets/ZoomOut';
import BackArrow from 'assets/BackIcon';
import ShowAllDocuments from 'pages/file/details/components/notes/ShowAllDocuments';
import { useParams } from 'react-router-dom';
import { actions as sliceActions } from 'pages/file/details/slice';
import { actions as commonSliceActions } from 'pages/common/slice';
import { DOCUMENT_SPLIT_TYPES, DOCUMENT_TYPES } from 'common/constants';
import { useGenerateDocs } from 'hooks/useGenerateDocs';
import { ZoomComponent } from '../Zoom/Zoom';
import Pagination from '../Pagination/Pagination';

const styles = {
  roundPagination: {
    display: 'flex',
    justifyContent: 'center',
    borderRadius: 25,
    alignItems: 'center',
    border: '1px solid #A4ABAE',
    position: 'absolute',
    top: 'calc(50% - 20px)',
    left: '-20px',
    zIndex: 1
  },
  closeButton: {
    position: 'absolute',
    right: '20px',
    top: '10px',
    borderRadius: '50px',
    background: '#fff',
    padding: 0,
    minWidth: '30px',
    height: '32px'
  }
};

const NewDocumentPreview = (props) => {
  const {
    previewItem = [],
    documentId = { from: 'inward' },
    from = 'summary',
    // isOneDocumentSelect,
    setIsOneDocumentSelect = () => { },
    expandEnable = false,
    setOpenNewExpand = () => { },
    openNewExpand,
    setFull = () => { },
    full,
    setShowingAllDocs = () => { },
    setIsOnSelectNote,
    showingAllDocuments,
    activeDocsFilterIndex = 0,
    setActiveDocsFilterIndex = () => {},
    setDocumentId
  } = props;

  const [page, setPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const [baseCode, setBaseCode] = useState(null);

  const [flag, setFlag] = useState(false);
  const [url, setUrl] = useState('');
  const [contentType, setContentType] = useState('');
  const [content, setContent] = useState({});

  const [rotateFlag, setRotateFlag] = useState(0);
  const [rotatePreviewFlag, setRotatePreviewFlag] = useState(0);
  const [loading, setLoading] = useState(false);
  const [zoom, setZoom] = useState(1);
  const params = useParams();

  const { previewData } = useGenerateDocs({
    url, flag, contentType, content
  });

  useEffect(() => {
    if (baseCode && previewData && url) {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
    return () => {
      if (baseCode) setBaseCode(null);
      setShowPreview(false);
    };
  }, [baseCode]);

  function getMimeType(arrayBuffer) {
    const bytes = new Uint8Array(arrayBuffer).subarray(0, 4);
    const header = bytes.reduce(
      (acc, byte) => acc + byte.toString(16).padStart(2, '0'),
      ''
    );

    switch (header) {
      case '25504446':
        return 'application/pdf'; // PDF magic number
      case 'ffd8ffe0':
      case 'ffd8ffe1':
      case 'ffd8ffe2':
      case 'ffd8ffe3':
      case 'ffd8ffe8':
        return 'image/jpeg'; // JPEG magic numbers
      case '89504e47':
        return 'image/png'; // PNG magic number
      case '47494638':
        return 'image/gif'; // GIF magic number
      default:
        return 'application/octet-stream'; // Unknown type
    }
  }

  // Handle zoom in
  const handleZoomIn = () => {
    setZoom((prevZoom) => Math.min(prevZoom + 0.2, 5));
  };

  // Handle zoom out
  const handleZoomOut = (event) => {
    event.preventDefault(); // Prevent the context menu from showing on right-click
    setZoom((prevZoom) => Math.max(prevZoom - 0.2, 1));
  };

  useEffect(() => {
    if (baseCode) {
      setBaseCode(null);
      setLoading(true);
    }
    if (previewData && url) {
      setRotatePreviewFlag(0);
      setLoading(true);
      setBaseCode(previewData);
    }
    return () => {
      if (baseCode) {
        setLoading(true);
        setBaseCode(null);
        setShowPreview(false);
      }
    };
  }, [previewData]);

  useEffect(() => {
    if (full) {
      setShowPreview(true);
    } else {
      setShowPreview(false);
    }
    return () => {
      setShowPreview(false);
      setBaseCode(null);
    };
  }, [full]);

  const handlePreviewIndexing = (item) => {
    const docId = item?.content?.notesDocumentId || item?.content?.fileId;
    const fromType = item?.content?.notesDocumentId ? 'note' : 'inward';
    setDocumentId({ docId, from: fromType });
  };

  useEffect(() => {
    if (previewItem && previewItem?.length > 0 && Object?.keys(documentId)?.length > 0) {
      const filteredArray = previewItem?.filter(
        (item) => (item?.content?.notesDocumentId || item?.content?.fileId) === documentId?.docId
      );
      const data = documentId?.docId ? filteredArray[0] : previewItem[0];

      setUrl(`${baseApiURL}${data?.link}`);
      setContent(data?.content);
      setContentType(data?.contentType);
      setFlag(!flag);
      setTotalItems(previewItem?.length);

      if (documentId?.docId) {
        const findIndex = previewItem?.findIndex(
          (item) => (item?.content?.notesDocumentId || item?.content?.fileId) === documentId?.docId
        );
        if (findIndex > -1) {
          setPage(findIndex + 1);
        }
      }
    } else {
      setTotalItems(0);
    }
  }, [JSON.stringify(previewItem), JSON.stringify(documentId)]);

  const handlePageIndex = (item) => {
    if (item === 'next') {
      return page + 1;
    }
    if (item === 'previous') {
      return page - 1;
    }
    return item;
  };

  const handlePage = (item) => {
    const data = previewItem[handlePageIndex(item) - 1];

    setBaseCode(null);
    setContentType(data?.contentType);
    setPage(handlePageIndex(item));

    // const docUrl = `${baseApiURL}${data?.link}?fileNo=${data?.content?.fileNo}&notesId=${data?.content?.notesId}&notesDocumentId=${data?.content?.notesDocumentId}&notesDocInfoId=${data?.content?.notesDocInfoId}`;
    // const docInwardUrl = `${baseApiURL}${data?.link}`;

    setUrl(`${baseApiURL}${data?.link}`);
    setContent(data?.content);
    setFlag(!flag);
  };

  const handleClose = () => {
    setShowPreview(false);
    setFull(false);
    setOpenNewExpand(false);
  };

  const handleFull = () => {
    if (!openNewExpand) {
      setOpenNewExpand(true);
    } else {
      setOpenNewExpand(false);
    }

    if (!full) {
      setFull(true);
    } else {
      setFull(false);
    }

    setShowPreview(true);
  };

  const documentTypesChack = (type) => {
    if (type && type === DOCUMENT_TYPES.PDF) {
      return 'pdf';
    }
    if (type && [DOCUMENT_TYPES.PNG, DOCUMENT_TYPES.JPG, DOCUMENT_TYPES.JPEG, DOCUMENT_TYPES.GIF]?.includes(type)) {
      return 'image';
    }
    return 'image';
  };

  const previewFunc = useCallback(
    (cType) => {
      const type = cType || contentType;
      if (loading || !baseCode || !type) {
        return (
          <div
            className="flex justify-center cursor-pointer mx-10 max-h-[750px] min-h-[750px]"
            aria-hidden="true"
          >
            <Spinner style={{ marginTop: '230px' }} />
          </div>
        );
      }
      return (
        <ZoomComponent
          image={baseCode}
          type={documentTypesChack(type)}
          zoom={zoom}
        />
      );
    },
    [loading, baseCode, zoom, contentType]
  );

  const downloadAck = () => {
    downloadBlob({
      blob: baseCode,
      fileName: `KSMART-FILE-DOCUMENT${handleContentType(contentType)}`
    });
  };

  const rotateAck = () => {
    if (rotateFlag === 270) {
      setRotateFlag(0);
    } else {
      setRotateFlag(rotateFlag + 90);
    }
  };

  const rotatePreview = () => {
    if (rotatePreviewFlag === 270) {
      setRotatePreviewFlag(0);
    } else {
      setRotatePreviewFlag(rotatePreviewFlag + 90);
    }
  };
  const printBlobUrl = async (blob) => {
    const response = await fetch(blob);
    const arrayBuffer = await response.arrayBuffer();
    const mimeType = getMimeType(arrayBuffer);
    const pdfBlob = new Blob([arrayBuffer], {
      type: mimeType || 'application/pdf'
    });
    const pdfUrl = URL.createObjectURL(pdfBlob);
    printBlob(pdfUrl);
  };

  const printAck = () => {
    printBlobUrl(baseCode);
  };

  const handleShowDocumentComponents = () => {
    setIsOneDocumentSelect({});
    setShowingAllDocs(false);
    setIsOnSelectNote('');
    setActiveDocsFilterIndex(0);
    return <ShowAllDocuments fileNo={params?.fileNo} />;
  };

  return (
    <>
      {!showPreview && previewItem && previewItem.map((item, index) => (
        index + 1 === page && (
          <div className="flex  bg-red-300">
            <div className="flex-none relative">
              <IconButton
                variant="unstyled"
                aria-label="Backward"
                icon={(
                  <Backward
                    name="backward"
                    color={page === 0 ? '#A4ABAE' : '#fff'}
                  />
                )}
                style={styles.roundPagination}
                background={page === 0 ? 'gray.200' : dark}
                onClick={() => handlePage('previous')}
                isDisabled={page === 1}
              />
            </div>
            {!full && (
            <IconButton
              onClick={() => handleShowDocumentComponents()}
              variant="unstyled"
              icon={<BackArrow color={dark} width="8" height="8" />}
            />
            )}
            <div className="flex-grow" style={{ overflow: 'scroll', scrollbarWidth: 'none' }}>
              <div className="p-0">
                <div className="flex items-center justify-end w-full mb-3">
                  {expandEnable && (
                    <Tooltip label={t('fullScreen')}>
                      <IconButton
                        variant="unstyled"
                        onClick={() => {
                          handlePreviewIndexing(item);
                          handleFull();
                        }}
                        leftIcon={
                          full ? (
                            <MiniScreenIcon width="21px" height="21px" />
                          ) : (
                            <FullScreenIcon width="21px" height="21px" />
                          )
                        }
                      />
                    </Tooltip>
                  )}
                  <Tooltip label={t('zoomIn')}>
                    <IconButton
                      variant="unstyled"
                      onClick={handleZoomIn}
                      leftIcon={
                        <ZoomIn width="21px" height="21px" color="#718096" />
                      }
                    />
                  </Tooltip>

                  <Tooltip label={t('zoomOut')}>
                    <IconButton
                      variant="unstyled"
                      onClick={handleZoomOut}
                      leftIcon={
                        <ZoomOut width="21px" height="21px" color="#718096" />
                      }
                    />
                  </Tooltip>

                  <Tooltip label={t('print')}>
                    <IconButton
                      variant="unstyled"
                      onClick={printAck}
                      leftIcon={<PrintIcon width="21px" height="21px" />}
                    />
                  </Tooltip>

                  <Tooltip label={t('download')}>
                    <IconButton
                      variant="unstyled"
                      onClick={downloadAck}
                      leftIcon={<DownloadIcon width="21px" height="21px" />}
                    />
                  </Tooltip>

                  <Tooltip label={t('rotate')}>
                    <IconButton
                      variant="unstyled"
                      onClick={rotateAck}
                      leftIcon={(
                        <Rotate
                          style={{ transform: `rotate(${rotateFlag}deg)` }}
                          width="21px"
                          height="21px"
                        />
                      )}
                    />
                  </Tooltip>
                  {full && (
                    <Tooltip label={t('close')}>
                      <IconButton
                        variant="unstyled"
                        onClick={handleClose}
                        leftIcon={<CloseOutlineIcon width="21px" height="21px" />}
                      />
                    </Tooltip>
                  )}

                  {showingAllDocuments && (
                    <div className="-mt-[8px]">
                      <select
                        value={activeDocsFilterIndex}
                        onChange={(event) => {
                          setBaseCode(null);
                          setContentType('');
                          setShowPreview(false);
                          setLoading(true);
                          setPage(0);
                          setDocumentId({ from: 'inward' });
                          setActiveDocsFilterIndex(Number(event.target.value));
                        }}
                        className="border-0 bg-transparent p-2 border-transparent focus:border-white outline-none"
                      >
                        {DOCUMENT_SPLIT_TYPES.map((data) => {
                          return (
                            <option key={data.id} value={data.id} selected={data.id === activeDocsFilterIndex}>
                              {data.name}
                            </option>
                          );
                        })}
                      </select>
                    </div>
                  )}
                </div>
              </div>
              <div
                style={{ transform: `rotate(${rotateFlag}deg)` }}
                key={item?.link}
              >
                <div
                  className="flex justify-center cursor-pointer mx-10 max-h-[750px] min-h-[750px] overflow-y-auto overflow-x-scroll"
                  aria-hidden="true"
                >
                  {loading || !baseCode ? (
                    <Spinner style={{ marginTop: '230px' }} />
                  ) : (
                    <div className="w-full">
                      {!loading && baseCode && <h4 className="mb-3">{item?.documentName}</h4>}
                      {previewFunc(documentId?.docId ? contentType : item.contentType)}
                    </div>
                  )}
                </div>
              </div>

              {previewItem?.length > 0 && (
                <Pagination
                  totalPages={previewItem?.length}
                  currentPage={page}
                  onPageChange={handlePage}
                />
              )}
            </div>
            <div className="flex-none relative">
              <IconButton
                variant="unstyled"
                aria-label="Forward"
                icon={(
                  <Forward
                    name="forward"
                    color={
                      totalItems / 3 === page || totalItems === 0
                        ? '#A4ABAE'
                        : '#fff'
                    }
                  />
                )}
                style={styles.roundPagination}
                onClick={() => handlePage('next')}
                background={
                  totalItems === page || totalItems === 0 ? 'gray.200' : dark
                }
                isDisabled={totalItems === page || totalItems === 0}
              />
            </div>
          </div>
        )
      ))}

      {!showPreview && previewItem?.length === 0 && (
        <div className="flex min-h-[750px]">
          <div className="flex-grow">
            <div className="p-0">
              {showingAllDocuments && (
                <div className="flex items-center justify-end w-full pr-3">
                  <div className="-mt-[4px]">
                    <select
                      value={activeDocsFilterIndex}
                      onChange={(event) => {
                        setBaseCode(null);
                        setContentType('');
                        setShowPreview(false);
                        setLoading(true);
                        setPage(0);
                        setDocumentId({ from: 'inward' });
                        setActiveDocsFilterIndex(Number(event.target.value));
                      }}
                      className="border-0 bg-transparent p-2 border-transparent focus:border-white outline-none"
                    >
                      {DOCUMENT_SPLIT_TYPES.map((data) => {
                        return (
                          <option key={data.id} value={data.id} selected={data.id === activeDocsFilterIndex}>
                            {data.name}
                          </option>
                        );
                      })}
                    </select>
                  </div>
                </div>
              )}
              <div className="flex items-center flex-col justify-center">
                <div className="w-[300px] mx-auto m-10 py-10">
                  <NoNotesIcon width="300px" />
                </div>
                {t('noDocumentFound')}
              </div>
            </div>
          </div>
        </div>
      )}

      <Modal
        isOpen={showPreview}
        size={full ? 'full' : '4xl'}
        onClose={handleClose}
        scrollBehavior="outside"
        blockScrollOnMount={false}
      >
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center justify-end w-full pt-2">
              {!from === 'summary' && (
                <Tooltip label={t('fullScreen')}>
                  <IconButton
                    variant="unstyled"
                    onClick={() => handleFull()}
                    leftIcon={
                      full ? (
                        <MiniScreenIcon width="21px" height="21px" />
                      ) : (
                        <FullScreenIcon width="21px" height="21px" />
                      )
                    }
                  />
                </Tooltip>
              )}
              <Tooltip label={t('zoomIn')}>
                <IconButton
                  variant="unstyled"
                  onClick={handleZoomIn}
                  leftIcon={
                    <ZoomIn width="21px" height="21px" color="#718096" />
                  }
                />
              </Tooltip>
              <Tooltip label={t('zoomOut')}>
                <IconButton
                  variant="unstyled"
                  onClick={handleZoomOut}
                  leftIcon={
                    <ZoomOut width="21px" height="21px" color="#718096" />
                  }
                />
              </Tooltip>
              <Tooltip label={t('print')}>
                <IconButton
                  variant="unstyled"
                  onClick={printAck}
                  leftIcon={<PrintIcon width="21px" height="21px" />}
                />
              </Tooltip>
              <Tooltip label={t('download')}>
                <IconButton
                  variant="unstyled"
                  onClick={downloadAck}
                  leftIcon={<DownloadIcon width="21px" height="21px" />}
                />
              </Tooltip>
              <Tooltip label={t('rotate')}>
                <IconButton
                  variant="unstyled"
                  onClick={rotatePreview}
                  leftIcon={(
                    <Rotate
                      style={{ transform: `rotate(${rotatePreviewFlag}deg)` }}
                      width="21px"
                      height="21px"
                    />
                  )}
                />
              </Tooltip>
              <Tooltip label={t('close')}>
                <IconButton
                  variant="unstyled"
                  onClick={handleClose}
                  leftIcon={<CloseOutlineIcon width="21px" height="21px" />}
                />
              </Tooltip>
            </div>
          </ModalHeader>
          <ModalBody>
            <div
              style={{
                height: full ? 'calc(100vh - 120px)' : 'calc(100vh - 250px)'
              }}
              className="overflow-y-auto"
            >
              <div style={{ transform: `rotate(${rotatePreviewFlag}deg)` }}>
                {previewFunc(contentType)}
              </div>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

const mapStateToProps = createStructuredSelector({
  documentId: getDocumentId,
  documentName: getDocumentNameFromNoteReferences,
  notes: getNotes,
  activeDocsFilterIndex: getActiveDocsFilterIndex
});

const mapDispatchToProps = (dispatch) => ({
  setShowingAllDocs: (data) => dispatch(sliceActions.setShowingAllDocs(data)),
  setIsOnSelectNote: (data) => dispatch(sliceActions.setIsOnSelectNote(data)),
  setDocumentId: (data) => dispatch(commonSliceActions.setDocumentId(data)),
  setActiveDocsFilterIndex: (data) => dispatch(sliceActions.setActiveDocsFilterIndex(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(NewDocumentPreview);
