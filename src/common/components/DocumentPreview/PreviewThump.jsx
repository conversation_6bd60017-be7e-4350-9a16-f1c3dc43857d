import { connect } from 'react-redux';
import { createStructuredSelector } from 'reselect';
import {
  Button,
  IconButton, Modal, ModalOverlay, ModalContent, ModalBody, t,
  ModalHeader,
  Tooltip
} from 'common/components';
import CloseIcon from 'assets/Close';
import React, { useState, useEffect } from 'react';
import ImageIcon from 'assets/Image';
import {
  PdfViewer, Spinner
} from '@ksmartikm/ui-components';
import View from 'assets/View';
import { actions as commonSliceActions } from 'pages/common/slice';
import MiniScreenIcon from 'assets/MiniScreen';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import Rotate from 'assets/Rotate';
import CloseOutlineIcon from 'assets/CloseOutline';
import FullScreenIcon from 'assets/FullScreen';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import { DOCUMENT_TYPES, handleContentType } from 'common/constants';
import { secondary } from 'utils/color';
import AttachmentIcon from 'assets/Attachment';
import { DocumentSvg, ExcelSvg, PdfSvg } from './Icons';

const PreviewThumb = (props) => {
  const {
    handlePreviewRemove, fileType, item, handlePreview, preview = {}, loading, from = 'normal', setAlertAction, index
  } = props;
  const [open, setOpen] = useState(false);
  const [full, setFull] = useState(false);
  const [rotatePreviewFlag, setRotatePreviewFlag] = useState(0);

  const handleOpen = () => {
    handlePreview(item);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  const handleDeleteClick = () => {
    handlePreviewRemove(item);
  };

  const handleConfirmDelete = () => {
    setAlertAction({
      open: true,
      variant: 'alert',
      message: t('areYouSureWantToDelete'),
      title: t('deleteConfirmation'),
      backwardActionText: t('no'),
      forwardActionText: t('yes'),
      forwardAction: () => handleDeleteClick()
    });
  };

  const previewBox = () => {
    switch (fileType) {
      case DOCUMENT_TYPES.PDF:
        return <PdfSvg />;
      case DOCUMENT_TYPES.EXCEL:
        return <ExcelSvg />;
      case DOCUMENT_TYPES.WORD:
        return <DocumentSvg />;
      default:
        return <ImageIcon />;
    }
  };

  const handleFull = () => {
    setFull(!full);
  };

  const rotatePreview = () => {
    if (rotatePreviewFlag === 270) {
      setRotatePreviewFlag(0);
    } else {
      setRotatePreviewFlag(rotatePreviewFlag + 90);
    }
  };

  // const printAck = () => {
  //   printBlob(preview);
  // };

  function getMimeType(arrayBuffer) {
    const bytes = new Uint8Array(arrayBuffer).subarray(0, 4);
    const header = bytes.reduce((acc, byte) => acc + byte.toString(16).padStart(2, '0'), '');
    switch (header) {
      case '25504446':
        return 'application/pdf';
      case 'ffd8ffe0':
      case 'ffd8ffe1':
      case 'ffd8ffe2':
      case 'ffd8ffe3':
      case 'ffd8ffe8':
        return 'image/jpeg';
      case '89504e47':
        return 'image/png';
      case '47494638':
        return 'image/gif';
      default:
        return 'application/octet-stream';
    }
  }
  const printBlobUrl = async (blob) => {
    const response = await fetch(blob);
    const arrayBuffer = await response.arrayBuffer();
    const mimeType = getMimeType(arrayBuffer);
    const pdfBlob = new Blob([arrayBuffer], { type: mimeType || 'application/pdf' });
    const pdfUrl = URL.createObjectURL(pdfBlob);
    printBlob(pdfUrl);
  };

  const printAck = () => {
    printBlobUrl(preview);
  };

  const downloadAck = () => {
    downloadBlob({ blob: preview, fileName: `KSMART-FILE-DOCUMENT${handleContentType(fileType)}` });
  };

  const [detectedType, setDetectedType] = useState(null);
  const [blobUrl, setBlobUrl] = useState(null);
  useEffect(() => {
    const detectContentType = async () => {
      if (typeof preview === 'string' && preview?.startsWith('blob:')) {
        try {
          const response = await fetch(preview);
          const contentType = response.headers.get('Content-Type');
          if (contentType === 'application/pdf' || contentType === 'blob') {
            setDetectedType('pdf');
            const blob = await response.blob();
            setBlobUrl(URL.createObjectURL(blob));
          } else if (contentType?.startsWith('image/')) {
            setDetectedType('image');
          } else {
            setDetectedType('unknown');
          }
        } catch (error) {
          setDetectedType('unknown');
          return error;
        }
      } else if (fileType === 'application/pdf') {
        setDetectedType('pdf');
        setBlobUrl(preview);
      }
      return null;
    };

    detectContentType();
  }, [preview, fileType]);

  const renderContent = () => {
    if (fileType?.includes('image') || detectedType === 'image') {
      return <img width="100%" src={preview} alt="Preview" />;
    }

    if (detectedType === 'pdf' || fileType === 'application/pdf' || (typeof preview === 'string' && preview?.startsWith('blob:'))) {
      return (
        <PdfViewer
          title="inward"
          width="100%"
          data={blobUrl || preview}
          aria-label="loading"
        />
      );
    }

    return <div>Unsupported file type</div>;
  };

  return (
    <>
      {
        from === 'table'
        && (
          <IconButton onClick={handleOpen} icon={<View />} variant="ghost" />

        )
      }
      {from === 'normal' && (
        <div className="preview-image mt-3">
          <Button className="preview-icon-button" onClick={handleOpen}>
            {previewBox()}
          </Button>
          <IconButton onClick={() => handleConfirmDelete()} icon={<CloseIcon color={secondary} />} />
        </div>
      )}

      {from === 'note' && (
        <div className="mt-3 px-1">
          <IconButton size="md" onClick={handleOpen} icon={previewBox()} />
        </div>
      )}
      {from === 'note-card' && (
        <div className="mt-3 px-1">
          <Button size="md" onClick={handleOpen} icon={previewBox()} leftIcon={<AttachmentIcon />}> {index} </Button>
        </div>
      )}

      <Modal isOpen={open} size={full ? 'full' : '4xl'} onClose={handleClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center justify-end w-full pt-2">
              <Tooltip label={full ? t('minimize') : t('maximize')}>
                <IconButton variant="unstyled" onClick={() => handleFull()} leftIcon={full ? <MiniScreenIcon width="21px" height="21px" /> : <FullScreenIcon width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('print')}>
                <IconButton variant="unstyled" onClick={printAck} leftIcon={<PrintIcon width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('download')}>
                <IconButton variant="unstyled" onClick={downloadAck} leftIcon={<DownloadIcon width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('rotate')}>
                <IconButton variant="unstyled" onClick={rotatePreview} leftIcon={<Rotate style={{ transform: `rotate(${rotatePreviewFlag}deg)` }} width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('close')}>
                <IconButton variant="unstyled" onClick={handleClose} leftIcon={<CloseOutlineIcon width="21px" height="21px" />} />
              </Tooltip>
            </div>
          </ModalHeader>
          <ModalBody>

            {loading ? <div style={{ height: full ? 'calc(100vh - 100px)' : 'calc(100vh - 250px)' }} className="overflow-y-auto mt-3"> <Spinner style={{ marginTop: '230px', marginLeft: 'calc(50% - 30px)' }} /> </div> : (
              <div style={{ height: full ? 'calc(100vh - 100px)' : 'calc(100vh - 250px)' }} className="overflow-y-auto mt-3">
                <div style={{ transform: `rotate(${rotatePreviewFlag}deg)` }}>
                  {renderContent()}
                </div>
              </div>
            )}

          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
};

const mapStateToProps = createStructuredSelector({

});

const mapDispatchToProps = (dispatch) => ({
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(PreviewThumb);
