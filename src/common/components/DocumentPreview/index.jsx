import React, { useState, useEffect } from 'react';
import {
  IconButton, Modal, ModalOverlay, ModalContent, ModalBody, PdfViewer, t, <PERSON><PERSON><PERSON>, ModalHeader
} from 'common/components';
import Forward from 'assets/Forward';
import Backward from 'assets/Backward';
import { dark } from 'utils/color';
import { Spinner } from '@ksmartikm/ui-components';
import { baseApiURL } from 'utils/http';
import { useGenerateDocs } from 'hooks/useGenerateDocs';
import NoNotesIcon from 'assets/NoNotesIcon';
import PrintIcon from 'assets/Printer';
import DownloadIcon from 'assets/Download';
import { printBlob } from 'utils/printBlob';
import { downloadBlob } from 'utils/downloadBlob';
import Rotate from 'assets/Rotate';
import FullScreenIcon from 'assets/FullScreen';
import CloseOutlineIcon from 'assets/CloseOutline';
import MiniScreenIcon from 'assets/MiniScreen';
import { DOCUMENT_SPLIT_TYPES, DOCUMENT_TYPES, handleContentType } from 'common/constants';

import { getOpenedPreviewIndex } from 'pages/common/selectors';
import { actions as commonSliceActions } from 'pages/common/slice';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import { getActiveDocsIndex } from 'pages/file/details/selector';
import { actions as fileSliceActions } from 'pages/file/details/slice';

const DocumentPreview = (props) => {
  const {
    preview = [],
    setOpenNewExpand = () => {},
    openNewExpand,
    setFull,
    full,
    setOpenedPreviewIndex,
    openedPreviewIndex,
    handleChangeActiveDocsIndex = () => { },
    activeDocsIndex,
    setActiveDocsIndex,
    ...rest
  } = props;
  const [page, setPage] = useState(0);
  const [totalItems, setTotalItems] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const [flag, setFlag] = useState(false);
  const [baseCode, setBaseCode] = useState(null);
  const [url, setUrl] = useState('');
  const [contentType, setContentType] = useState('');
  const [content, setContent] = useState({});

  const [rotateFlag, setRotateFlag] = useState(0);
  const [rotatePreviewFlag, setRotatePreviewFlag] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  const { previewData } = useGenerateDocs({
    url, flag, contentType, content
  });

  useEffect(() => {
    setShowPreview(rest?.showPreviewProps);
  }, [JSON.stringify(rest)]);

  useEffect(() => {
    if (baseCode && previewData && url) {
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
    }
    return () => {
      if (baseCode) setBaseCode(null);
    };
  }, [baseCode]);

  useEffect(() => {
    if ((!openedPreviewIndex || openedPreviewIndex === 0) && preview?.length > 0) {
      setTotalItems(preview?.length);
      setUrl(`${baseApiURL}${preview[0].link}`);
      setContentType(preview[0].contentType);
      setContent(preview[0].content);
      setFlag(!flag);
    } else if (openedPreviewIndex > 0 && preview?.length > 0) {
      setPage(openedPreviewIndex);

      setTotalItems(preview?.length);
      setUrl(`${baseApiURL}${preview[openedPreviewIndex].link}`);
      setContentType(preview[openedPreviewIndex].contentType);
      setContent(preview[openedPreviewIndex].content);
      setFlag(!flag);
    } else {
      setTotalItems(0);
    }
  }, [JSON.stringify(preview), JSON.stringify(openedPreviewIndex)]);

  const styles = {
    roundPagination: {
      display: 'flex',
      justifyContent: 'center',
      borderRadius: 25,
      alignItems: 'center',
      border: '1px solid #A4ABAE',
      position: 'absolute',
      top: 'calc(50% - 20px)',
      left: '-20px',
      zIndex: 1
    },
    closeButton: {
      position: 'absolute',
      right: '20px',
      top: '10px',
      borderRadius: '50px',
      background: '#fff',
      padding: 0,
      minWidth: '30px',
      height: '32px'
    }
  };

  const handlePage = (item) => {
    setFlag(!flag);
    if (item === 'next') {
      setUrl(`${baseApiURL}${preview[page + 1].link}`);
      setContentType(preview[page + 1].contentType);
      setContent(preview[page + 1].content);
      setFlag(!flag);
      setPage(page + 1);
    } else if (page !== 0) {
      setUrl(`${baseApiURL}${preview[page - 1].link}`);
      setContentType(preview[page - 1].contentType);
      setContent(preview[page - 1].content);
      setFlag(!flag);
      setPage(page - 1);
    }
  };

  useEffect(() => {
    if (baseCode) {
      setBaseCode(null);
      setIsLoading(true);
    }
    if (previewData && url) {
      setIsLoading(true);
      setBaseCode(previewData);
    // } else {
    //   setIsLoading(false);
    }
    return () => {
      if (baseCode) {
        setIsLoading(true);
        setBaseCode(null);
      }
    };
  }, [previewData]);

  const handlePreview = () => {
    setShowPreview(true);
    setRotatePreviewFlag(0);
  };

  const handleClose = () => {
    setShowPreview(false);
    setFull(false);
    setOpenNewExpand(false);
    setBaseCode(null);
    rest?.setIsOpenDocumentModal?.(false);
  };

  const handleFull = () => {
    if (!openNewExpand) {
      setOpenNewExpand(true);
      // setShowPreview(false);
    } else {
      setOpenNewExpand(false);
    }
    if (!full) {
      setFull(true);
    } else {
      setFull(false);
    }
  };

  const previewFunc = (cType) => {
    let contentReturn = isLoading ? (
      <div
        className="flex justify-center mx-10 max-h-[750px]"
        aria-hidden="true"
      >
        <Spinner style={{ marginTop: '230px' }} />
      </div>
    ) : (
      <img
        title="File Documents"
        style={{ background: 'none', borderRadius: '8px' }}
        width="100%"
        src={baseCode}
        aria-label="loading"
      />
    );

    if (!isLoading && !cType) {
      contentReturn = (
        <PdfViewer
          data={baseCode}
          type="scroll"
          variant="normal"
        />
      );
    }

    if (!isLoading && cType === DOCUMENT_TYPES.PDF) {
      contentReturn = (
        <PdfViewer
          data={baseCode}
          type="scroll"
          variant="normal"
        />
      );
    }

    if (!isLoading && cType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      contentReturn = (
        <iframe
          data={baseCode}
          title="excel"
        />
      );
    }

    return contentReturn;
  };

  // const printAck = () => {
  //   printBlob(baseCode);
  // };

  function getMimeType(arrayBuffer) {
    const bytes = new Uint8Array(arrayBuffer).subarray(0, 4);
    const header = bytes.reduce((acc, byte) => acc + byte.toString(16).padStart(2, '0'), '');
    switch (header) {
      case '25504446':
        return 'application/pdf';
      case 'ffd8ffe0':
      case 'ffd8ffe1':
      case 'ffd8ffe2':
      case 'ffd8ffe3':
      case 'ffd8ffe8':
        return 'image/jpeg';
      case '89504e47':
        return 'image/png';
      case '47494638':
        return 'image/gif';
      default:
        return 'application/octet-stream';
    }
  }
  const printBlobUrl = async (blob) => {
    const response = await fetch(blob);
    const arrayBuffer = await response.arrayBuffer();
    const mimeType = getMimeType(arrayBuffer);
    const pdfBlob = new Blob([arrayBuffer], { type: mimeType || 'application/pdf' });
    const pdfUrl = URL.createObjectURL(pdfBlob);
    printBlob(pdfUrl);
  };

  const printAck = () => {
    printBlobUrl(baseCode);
  };

  const downloadAck = () => {
    downloadBlob({ blob: baseCode, fileName: `KSMART-FILE-DOCUMENT${handleContentType(contentType)}` });
  };

  const rotateAck = () => {
    if (rotateFlag === 270) {
      setRotateFlag(0);
    } else {
      setRotateFlag(rotateFlag + 90);
    }
  };

  const rotatePreview = () => {
    if (rotatePreviewFlag === 270) {
      setRotatePreviewFlag(0);
    } else {
      setRotatePreviewFlag(rotatePreviewFlag + 90);
    }
  };

  useEffect(() => {
    if (parseInt(activeDocsIndex, 10) !== '') {
      handleChangeActiveDocsIndex(parseInt(activeDocsIndex, 10));
    }
  }, [JSON.stringify(activeDocsIndex)]);

  return (
    <div className="max-h-24">
      {preview.map((item, index) => (
        index === page && (
          <div className="flex min-h-[750px]" key={content}>
            <div className="flex-none relative">
              <IconButton
                variant="unstyled"
                aria-label="Backward"
                icon={<Backward name="backward" color={page === 0 ? '#A4ABAE' : '#fff'} />}
                style={styles.roundPagination}
                className={`transition-opacity duration-500 ${!isLoading ? 'opacity-100' : 'opacity-75'}`}
                background={page === 0 ? 'gray.200' : dark}
                onClick={() => handlePage('previous')}
                isDisabled={page === 0}
              />
            </div>
            <div className="flex-grow">
              <div className="p-0">
                <div className={`flex items-center ${rest?.isDocumentNameShow && item?.documentName && item?.documentName !== '' ? 'flex-nowrap justify-between' : 'justify-end'} w-full pr-3`}>
                  {(rest?.isDocumentNameShow && item?.documentName) && (
                  <Tooltip label={item?.documentName}>
                    <span className="font-semibold text-[#09327B] !w-[278px] px-1">
                      {item?.documentName?.length > 25 ? `${item?.documentName?.slice(0, 25)}...` : item?.documentName}
                    </span>
                  </Tooltip>
                  )}
                  <div className="flex items-center justify-end">
                    <Tooltip label={full ? t('minScreen') : t('fullScreen')}>
                      <IconButton
                        variant="unstyled"
                        onClick={() => {
                          handleFull();
                          setOpenedPreviewIndex(index);
                        }}
                        leftIcon={full ? <MiniScreenIcon width="21px" height="21px" /> : <FullScreenIcon width="21px" height="21px" />}
                      />
                    </Tooltip>
                    <Tooltip label={t('print')}>
                      <IconButton variant="unstyled" onClick={printAck} leftIcon={<PrintIcon width="21px" height="21px" />} />
                    </Tooltip>
                    <Tooltip label={t('download')}>
                      <IconButton variant="unstyled" onClick={downloadAck} leftIcon={<DownloadIcon width="21px" height="21px" />} />
                    </Tooltip>
                    <Tooltip label={t('rotate')}>
                      <IconButton variant="unstyled" onClick={rotateAck} leftIcon={<Rotate style={{ transform: `rotate(${rotateFlag}deg)` }} width="21px" height="21px" />} />
                    </Tooltip>
                    {openNewExpand && (
                      <Tooltip label={t('close')}>
                        <IconButton variant="unstyled" onClick={handleClose} leftIcon={<CloseOutlineIcon width="21px" height="21px" />} />
                      </Tooltip>
                    )}
                    {rest?.isDocumentTypesShow && (
                      <div className="-mt-[8px]">
                        <select
                          value={activeDocsIndex}
                          onChange={(event) => {
                            setPage(0);
                            setActiveDocsIndex(event.target.value);
                          }}
                          className="border-0 bg-transparent p-2 border-transparent focus:border-white outline-none"
                        >
                          {DOCUMENT_SPLIT_TYPES.map((data) => {
                            return (
                              <option key={data.id} value={data.id} selected={data.id === activeDocsIndex}>
                                {data.name}
                              </option>
                            );
                          })}
                        </select>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div style={{ transform: `rotate(${rotateFlag}deg)` }}>
                <div className="flex justify-center cursor-pointer px-10 max-h-[750px] min-h-[750px] overflow-y-auto" aria-hidden="true" onClick={() => handlePreview()}>
                  <div>
                    {previewFunc(item.contentType)}
                  </div>
                </div>
              </div>

            </div>
            <div className="flex-none relative">
              <IconButton
                variant="unstyled"
                aria-label="Forward"
                icon={<Forward name="forward" color={(totalItems / 3 === (page + 1)) || totalItems === 0 ? '#A4ABAE' : '#fff'} />}
                style={styles.roundPagination}
                className={`transition-opacity duration-500 ${!isLoading ? 'opacity-100' : 'opacity-50'}`}
                onClick={() => handlePage('next')}
                background={(totalItems === (page + 1) || totalItems === 0) ? 'gray.200' : dark}
                isDisabled={totalItems === (page + 1) || totalItems === 0}
              />
            </div>
          </div>
        )
      ))}

      {
        preview?.length === 0
        && (
          <div className="flex min-h-[750px]">
            <div className="flex-grow">
              <div className="p-0">
                {rest?.isDocumentTypesShow && (
                  <div className="flex items-center justify-end w-full pr-3">
                    <div className="-mt-[4px]">
                      <select
                        value={activeDocsIndex}
                        onChange={(event) => {
                          setPage(0);
                          setActiveDocsIndex(event.target.value);
                        }}
                        className="border-0 bg-transparent p-2 border-transparent focus:border-white outline-none"
                      >
                        {DOCUMENT_SPLIT_TYPES.map((data) => {
                          return (
                            <option key={data.id} value={data.id} selected={data.id === activeDocsIndex}>
                              {data.name}
                            </option>
                          );
                        })}
                      </select>
                    </div>
                  </div>
                )}
                <div className="flex items-center flex-col justify-center">
                  <div className="w-[300px] mx-auto m-10 py-10">
                    <NoNotesIcon width="300px" />
                  </div>
                  {t('noDocumentFound')}
                </div>
              </div>
            </div>
          </div>
        )
      }

      <Modal isOpen={showPreview} size={full ? 'full' : '4xl'} onClose={handleClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center justify-end w-full pt-2">
              <Tooltip label={t('fullScreen')}>
                <IconButton
                  variant="unstyled"
                  onClick={() => {
                    handleFull();
                    setOpenedPreviewIndex(page);
                  }}
                  leftIcon={full ? <MiniScreenIcon width="21px" height="21px" /> : <FullScreenIcon width="21px" height="21px" />}
                />
              </Tooltip>
              <Tooltip label={t('print')}>
                <IconButton variant="unstyled" onClick={printAck} leftIcon={<PrintIcon width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('download')}>
                <IconButton variant="unstyled" onClick={downloadAck} leftIcon={<DownloadIcon width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('rotate')}>
                <IconButton variant="unstyled" onClick={rotatePreview} leftIcon={<Rotate style={{ transform: `rotate(${rotatePreviewFlag}deg)` }} width="21px" height="21px" />} />
              </Tooltip>
              <Tooltip label={t('close')}>
                <IconButton variant="unstyled" onClick={handleClose} leftIcon={<CloseOutlineIcon width="21px" height="21px" />} />
              </Tooltip>
            </div>
          </ModalHeader>
          <ModalBody>
            <div style={{ height: full ? 'calc(100vh - 120px)' : 'calc(100vh - 250px)' }} className="overflow-y-auto">
              <div style={{ transform: `rotate(${rotatePreviewFlag}deg)` }}>
                {previewFunc(contentType)}
              </div>
            </div>
          </ModalBody>
        </ModalContent>

      </Modal>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  openedPreviewIndex: getOpenedPreviewIndex,
  activeDocsIndex: getActiveDocsIndex
});

const mapDispatchToProps = (dispatch) => ({
  setOpenedPreviewIndex: (data) => dispatch(commonSliceActions.setOpenedPreviewIndex(data)),
  setActiveDocsIndex: (data) => dispatch(fileSliceActions.setActiveDocsIndex(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(DocumentPreview);
