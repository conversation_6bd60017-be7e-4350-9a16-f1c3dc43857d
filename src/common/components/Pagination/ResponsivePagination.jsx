import PaginationLeftIcon from 'assets/PaginationLeftIcon';
import PaginationRightIcon from 'assets/PaginationRightIcon';
import React from 'react';

const ResponsivePagination = ({
  currentPage = 1,
  itemsPerPage = 10,
  totalItems = 0,
  onPageChange = () => {},
  boundaryCount = 1
}) => {
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const buttons = [];

  const startButton = Math.max(1, currentPage - boundaryCount);
  const endButton = Math.min(totalPages, currentPage + boundaryCount);

  const onNext = () => {
    onPageChange(currentPage + 1);
  };

  const onPrevious = () => {
    onPageChange(currentPage - 1);
  };

  if (startButton > 1) {
    buttons.push(
      <button key={1} onClick={() => onPageChange(1)} className={`px-3 py-1  ${currentPage === 1 ? 'text-white !bg-[#09327B] !border-[#09327B]' : 'text-[#454545] bg-[#E8ECEE] hover:bg-gray-200'} border border-[#E8ECEE] rounded-md text-[14px]`}>
        1
      </button>
    );
    if (startButton > 2) {
      buttons.push(<span key="dots-start" className="">...</span>);
    }
  }
  for (let page = startButton; page <= endButton; page += 1) {
    buttons.push(
      <button
        key={page}
        onClick={() => onPageChange(page)}
        className={`px-3 py-1  ${(currentPage) === page ? 'text-white !bg-[#09327B] !border-[#09327B]' : 'text-[#454545] bg-[#E8ECEE] hover:bg-gray-200'} border border-[#E8ECEE] rounded-md text-[14px]`}
      >
        {page}
      </button>
    );
  }

  if (endButton < totalPages) {
    if (endButton < totalPages - 1) {
      buttons.push(<span key="dots-end">...</span>);
    }
    buttons.push(
      <button
        key={totalPages}
        onClick={() => onPageChange(totalPages)}
        className={`px-3 py-1  ${(currentPage) === totalPages ? 'text-white !bg-[#09327B] !border-[#09327B]' : 'text-[#454545] bg-[#E8ECEE] hover:bg-gray-200'} border border-[#E8ECEE] rounded-md text-[14px]`}
      >
        {totalPages}
      </button>
    );
  }
  return (
    <div className="flex items-center justify-center mt-4">
      <button
        aria-label="previous-page"
        onClick={onPrevious}
        disabled={currentPage < 2}
        className={`px-3 py-1  text-gray-500 bg-white border rounded-md disabled:opacity-50 h-8 w-8 ${currentPage < 2 ? 'border-[#E8ECEE]' : 'border-[#09327B]'}`}
      >
        <PaginationLeftIcon color="#09327B" className="w-5 h-5" />
      </button>
      {buttons}
      <button
        aria-label="next-page"
        onClick={onNext}
        disabled={(currentPage) === totalPages}
        className={`px-3 py-1  text-gray-500 bg-white border rounded-md disabled:opacity-50 h-8 w-8 ${(currentPage) === totalPages ? 'border-[#E8ECEE]' : 'border-[#09327B]'}`}
      >
        <PaginationRightIcon color="#09327B" className="w-5 h-5" />
      </button>
    </div>
  );
};

export default ResponsivePagination;
