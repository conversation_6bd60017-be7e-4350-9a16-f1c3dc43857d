import axios from 'axios';
import queryString from 'query-string';
import <PERSON>ck<PERSON><PERSON>pter from 'axios-mock-adapter';
import {
  describe, it, expect, afterEach
} from 'vitest';
import {
  getRequest, putRequest, postRequest, patchRequest, deleteRequest
} from './axios';

const mock = new MockAdapter(axios);

describe('API Request Functions', () => {
  const baseURL = 'http://example.com';
  const URL = 'test-endpoint';

  afterEach(() => {
    mock.reset();
  });

  it('getRequest should make a GET request and return the response', async () => {
    const query = { param: 'value' };
    const config = {};
    const payload = { data: query, config, baseURL };
    const responseData = { success: true };

    mock.onGet(queryString.stringifyUrl({ url: `${baseURL}/${URL}`, query })).reply(200, responseData);

    const response = await getRequest(URL, payload);

    expect(response.data).toEqual(responseData);
  });

  it('putRequest should make a PUT request and return the response', async () => {
    const data = { key: 'value' };
    const config = {};
    const payload = { data, config, baseURL };
    const responseData = { success: true };

    mock.onPut(`${baseURL}/${URL}`, data).reply(200, responseData);

    const response = await putRequest(URL, payload);

    expect(response.data).toEqual(responseData);
  });

  it('postRequest should make a POST request and return the response', async () => {
    const data = { key: 'value' };
    const config = {};
    const payload = { data, config, baseURL };
    const responseData = { success: true };

    mock.onPost(`${baseURL}/${URL}`, data).reply(200, responseData);

    const response = await postRequest(URL, payload);

    expect(response.data).toEqual(responseData);
  });

  it('patchRequest should make a PATCH request and return the response', async () => {
    const data = { key: 'value' };
    const config = {};
    const payload = { data, config, baseURL };
    const responseData = { success: true };

    mock.onPatch(`${baseURL}/${URL}`, data).reply(200, responseData);

    const response = await patchRequest(URL, payload);

    expect(response.data).toEqual(responseData);
  });

  it('deleteRequest should make a DELETE request and return the response', async () => {
    const query = { param: 'value' };
    const config = {};
    const payload = { data: query, config, baseURL };
    const responseData = { success: true };

    mock.onDelete(queryString.stringifyUrl({ url: `${baseURL}/${URL}`, query })).reply(200, responseData);

    const response = await deleteRequest(URL, payload);

    expect(response.data).toEqual(responseData);
  });
});
