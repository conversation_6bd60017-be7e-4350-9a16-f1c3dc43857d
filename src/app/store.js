import { combineReducers, configureStore } from '@reduxjs/toolkit';
import logger from 'redux-logger';
import createSagaMiddleware from 'redux-saga';
import rootReducer from './rootReducers';
import rootSaga from './rootSaga';

const sagaMiddleware = createSagaMiddleware();

const middleWares = [];
middleWares.push(sagaMiddleware);

if (import.meta.env.MODE === 'development') {
  middleWares.push(logger);
}

const reducers = combineReducers({
  ...rootReducer
});

export const store = configureStore({
  reducer: reducers,
  devTools: true,
  middleware: middleWares
});

sagaMiddleware.run(rootSaga);

export function setupStore(preloadedState) {
  return configureStore({
    reducer: rootReducer,
    preloadedState
  });
}
