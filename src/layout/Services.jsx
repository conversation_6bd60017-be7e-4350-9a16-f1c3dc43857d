import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import MainHeader from 'common/components/MainHeader';
import FileTrackingHeader from './components/HeaderSection';

const Services = () => {
  const location = useLocation();
  return (
    <div className="w-screen h-screen">
      {location.pathname.includes('file-management/public/file-tracking') ? <FileTrackingHeader /> : <MainHeader />}
      <div
        className="flex flex-wrap gap-4 px-4 sm:px-10 md:px-20 py-4"
        style={{ background: '#E7EFF5' }}
      >
        <div className="grow pt-20">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default Services;
