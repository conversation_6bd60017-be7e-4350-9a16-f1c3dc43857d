/* eslint-disable react/prop-types */
import React, { useState, useEffect } from 'react';
import {
  Outlet,
  useParams,
  useNavigate,
  useLocation,
  useSearchParams
} from 'react-router-dom';
import FileHead from 'common/components/Header';
import { createStructuredSelector } from 'reselect';
import { connect } from 'react-redux';
import {
  getFileDetails,
  getInterpellationPopupOpen,
  getMergeLinkFiles,
  getSummaryBackButton
} from 'pages/file/details/selector';
import { dark, light } from 'utils/color';
import MainHeader from 'common/components/MainHeader';
import { MENU } from 'common/GeneralMenu';
import {
  BASE_PATH,
  FINANCE_MODULES,
  MEETING_MANAGEMENT_MODULES
} from 'common/constants';
import {
  actions as detailsActions,
  actions as sliceActions
} from 'pages/file/details/slice';
import { FILE_ROLE, FILE_STATUS } from 'pages/common/constants';
import { generalFeatureMenu } from 'common/menuHandler';
import { routeRedirect } from 'utils/common';
// import Summary from 'assets/Summary';
import LinkIcon from 'assets/Link';
import UnLink from 'assets/UnLink';
import Merge from 'assets/Merge';
import UnMerge from 'assets/UnMerge';
import Beneficiary from 'assets/Beneficiary';
import { t } from 'i18next';
import { getPostIdByPenNoDetails, getUserInfo } from 'pages/common/selectors';
import * as commonActions from 'pages/common/actions';
import * as actions from 'pages/file/details/actions';
import { GeneralFeatureWithSearch } from 'common/components';
import { hasLbType } from 'utils/user';
import Interpellation from 'pages/file/details/components/summaryDetails/interpellation';
import { PartFileIcon } from '../assets/Svg';
import FileHeader from './components/FileHeader';

export const BASE_UI_PATH = `${window.location.origin}/ui`;

const File = ({
  fileDetails,
  userInfo: { userDetails: { pen = '' } = {} } = {},
  fetchPostIdByPenNo,
  postIdByPenNoDetails,
  // setMergedBackButton,
  // fetchFileDetails,
  mergeLinkFiles,
  fetchMergeLink,
  // summaryButtonFlag,
  // setSummaryBackButton,
  setInterpellationPop,
  isAuditor = false,
  interpellationPop
}) => {
  const params = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [breadItems, setBreadItems] = useState([]);
  const [linkedOptions, setLinkedOptions] = useState([]);
  const [mergedOptions, setMergedOptions] = useState([]);
  const DEMAND_SERVICES = ['DFCS13', 'DFCS14', 'DFCS15'];
  const BUISNESS_FACILITATION_SERVICES = ['BFIF01', 'BFIF02', 'BFIF03', 'BFIF07', 'BFHP02', 'BFHP03'];

  // const from = searchParams.get('from');

  useEffect(() => {
    if (params?.fileNo) {
      fetchMergeLink(params?.fileNo);
    }
  }, [params?.fileNo]);

  useEffect(() => {
    if (mergeLinkFiles?.MERGING?.length > 0) {
      const merged = mergeLinkFiles?.MERGING?.map((item) => ({
        id: item,
        fileNo: item,
        name: t('fileNumber')
      }));
      setMergedOptions(merged);
    } else {
      setMergedOptions([]);
    }
    if (mergeLinkFiles?.LINKED?.length > 0) {
      const linked = mergeLinkFiles?.LINKED?.map((item) => ({
        id: item,
        fileNo: item,
        name: t('fileNumber')
      }));
      setLinkedOptions(linked);
    } else {
      setLinkedOptions([]);
    }
  }, [mergeLinkFiles]);

  const disableUnmegeMenu = () => {
    if (fileDetails?.postId !== fileDetails?.custodian?.id) {
      return true;
    } if (mergedOptions?.length === 0 || mergedOptions === undefined) {
      return true;
    }
    return false;
  };

  const featureMenu = (status) => {
    const menuUpdate = MENU[0]?.child;
    const menuArray = MENU;

    if (status === FILE_STATUS.CLOSED) {
      return menuArray?.map((section) => ({
        ...section,
        child: section.child.filter((item) => item.key === 'custodian-change')
      }));
    }

    const disableMenuItem = (key) => {
      const findIndex = menuUpdate.findIndex((item) => item.key === key);
      if (findIndex !== -1) {
        menuArray[0].child[findIndex].isHide = true;
      }
    };

    const enableMenuItem = (key) => {
      const findIndex = menuUpdate.findIndex((item) => item.key === key);
      if (findIndex !== -1) {
        menuArray[0].child[findIndex].isHide = false;
      }
    };

    if (fileDetails?.role) {
      if (![FILE_ROLE.OPERATOR, FILE_ROLE.VERIFIER, FILE_ROLE.APPROVER, FILE_ROLE.IMPLEMENTING_OFFICER, FILE_ROLE.ADMINISTRATOR].includes(fileDetails.role)) {
        disableMenuItem('custodian-change');
      }
      if (fileDetails.serviceCode !== 'HRET01') {
        disableMenuItem('salary');
      }
      if (fileDetails.serviceCode !== 'FMAS42') {
        disableMenuItem('recoveryPayment');
      }
      if (fileDetails.serviceCode !== 'FMBD01') {
        disableMenuItem('budget');
      }
      if (fileDetails.serviceCode !== 'FMBD39' || fileDetails.applicantName === null) {
        disableMenuItem('projectView');
      }
      // DEMAND
      if (hasLbType([3, 4]) && DEMAND_SERVICES.includes(fileDetails?.serviceCode)) {
        disableMenuItem('demand');
      } else if (hasLbType([5]) && DEMAND_SERVICES.includes(fileDetails?.serviceCode)) {
        enableMenuItem('demand');
      } else if (
        fileDetails?.moduleCode === 'BP'
        && fileDetails?.source === 1
        && hasLbType([5])
      ) {
        disableMenuItem('demand');
      } else if (fileDetails?.moduleCode === 'BP' && fileDetails?.source === 1) {
        disableMenuItem('demand');
      } else if (fileDetails?.moduleCode === 'BF' && BUISNESS_FACILITATION_SERVICES.includes(fileDetails?.serviceCode)) {
        disableMenuItem('demand');
      } else {
        enableMenuItem('demand');
      }
      if (fileDetails.serviceCode !== 'FMAS52') {
        disableMenuItem('standing-demand');
      }
      if (fileDetails?.postId !== fileDetails?.custodian?.id) {
        disableMenuItem('merge-file');
      }
      if (fileDetails.serviceCode !== 'FMAS60') {
        disableMenuItem('imprestClaim');
      }
      if (disableUnmegeMenu()) {
        disableMenuItem('unmerge');
      } else if (disableUnmegeMenu() === false) {
        enableMenuItem('unmerge');
      }
      if (linkedOptions?.length === 0 || linkedOptions === undefined) {
        disableMenuItem('un-link-file');
      } else {
        enableMenuItem('un-link-file');
      }
      if (fileDetails.source === 1 && fileDetails.serviceCode !== 'FMBD39') {
        disableMenuItem('demandCancellation');
      }
      if (fileDetails.serviceCode !== 'FMAS03') {
        disableMenuItem('refund');
      }

      if (fileDetails.serviceCode !== 'FMAS59') {
        disableMenuItem('imprestDisbursement');
      }

      if (fileDetails.serviceCode !== 'FMAS35') {
        disableMenuItem('contraEntry');
      }

      if (fileDetails.serviceCode !== 'FMAS37') {
        disableMenuItem('journalEntry');
      }
    }

    const modifyInterUnitMenuItem = (key) => {
      const findIndex = menuArray[0].child.findIndex((item) => item.key === key);
      if (findIndex !== -1) menuArray[0].child[findIndex].isHide = true;
    };

    if (fileDetails?.hasInterUnitRunning) ['custodian-change', 'merge-file', 'unmerge', 'requisition', 'requisitionAllotment', 'inward-de-link', 'paymentOrder', 'payment'].forEach((item) => modifyInterUnitMenuItem(item));

    if (fileDetails && fileDetails.source !== 7) {
      modifyInterUnitMenuItem('interpellation');
    }

    if (fileDetails && fileDetails.source === 7) {
      disableMenuItem('agenda');
    }

    return menuArray;
  };

  const submit = (data) => {
    if (data?.isHide) {
      return;
    }

    if (data.key === 'interpellation') {
      setInterpellationPop(true);
      return;
    }

    const moduleKeyToUrl = {
      [FINANCE_MODULES.DEMAND.KEY]: FINANCE_MODULES.DEMAND.URL,
      [FINANCE_MODULES.CLAIM.KEY]: FINANCE_MODULES.CLAIM.URL,
      [FINANCE_MODULES.PAYMENT_ORDER.KEY]: FINANCE_MODULES.PAYMENT_ORDER.URL,
      [FINANCE_MODULES.PAYMENT.KEY]: FINANCE_MODULES.PAYMENT.URL,
      [FINANCE_MODULES.BUDGET.KEY]: FINANCE_MODULES.BUDGET.URL,
      [FINANCE_MODULES.RECEIPT.KEY]: FINANCE_MODULES.RECEIPT.URL,
      [FINANCE_MODULES.SALARY.KEY]: FINANCE_MODULES.SALARY.URL,
      [FINANCE_MODULES.RECOVERY_PAYMENT.KEY]:
        FINANCE_MODULES.RECOVERY_PAYMENT.URL,
      [FINANCE_MODULES.STANDING_DEMAND.KEY]:
        FINANCE_MODULES.STANDING_DEMAND.URL,
      [FINANCE_MODULES.REQUISITION.KEY]: FINANCE_MODULES.REQUISITION.URL,
      [FINANCE_MODULES.OWN_FUND_PAYMENT.KEY]:
        FINANCE_MODULES.OWN_FUND_PAYMENT.URL,
      [FINANCE_MODULES.REQUISITION_ALLOTMENT.KEY]:
        FINANCE_MODULES.REQUISITION_ALLOTMENT.URL,
      [FINANCE_MODULES.DEMAND_CANCELLATION.KEY]:
        FINANCE_MODULES.DEMAND_CANCELLATION.URL,
      [MEETING_MANAGEMENT_MODULES.AGENDA.KEY]:
        MEETING_MANAGEMENT_MODULES.AGENDA.URL,
      [FINANCE_MODULES.BILL_GENERATE.KEY]: FINANCE_MODULES.BILL_GENERATE.URL,
      [FINANCE_MODULES.BILL_SEND.KEY]: FINANCE_MODULES.BILL_SEND.URL,
      [FINANCE_MODULES.ADVANCE_CLAIM.KEY]: FINANCE_MODULES.ADVANCE_CLAIM.URL,
      [FINANCE_MODULES.IMPREST_CLAIM.KEY]: FINANCE_MODULES.IMPREST_CLAIM.URL,
      [FINANCE_MODULES.REFUND.KEY]: FINANCE_MODULES.REFUND.URL,
      [FINANCE_MODULES.CONTRA_ENTRY.KEY]: FINANCE_MODULES.CONTRA_ENTRY.URL,
      [FINANCE_MODULES.JOURNAL_ENTRY.KEY]: FINANCE_MODULES.JOURNAL_ENTRY.URL,
      [FINANCE_MODULES.IMPREST_DISBURSEMENT.KEY]:
        FINANCE_MODULES.IMPREST_DISBURSEMENT.URL
    };

    const url = moduleKeyToUrl[data?.key];
    if (url) window.location.href = `${url}/${params?.fileNo}`;
    else if (data?.key === 'record-room-request') navigate(`${BASE_PATH}/services/record-room/access-room/${params?.fileNo}`); // services/record-room/section/request
    else if (data?.key === 'view-record-room-request') navigate(`${BASE_PATH}/services/record-room/section/requested/${params?.fileNo}`);
    // else if (data?.key === 'part-file') navigate(`${BASE_PATH}/services/part-file/${params?.fileNo}`);
    else navigate(`${BASE_PATH}/file/${params?.fileNo}/${data?.key}`);
  };

  const activeColor = (activeProp) => {
    return activeProp ? dark : '#5C6E93';
  };

  const isActiveByLocation = (type) => {
    const paths = {
      summary: '/summary',
      note: '/notes',
      draft: '/draft',
      filelink: '/file-link',
      fileUnLink: '/un-link-file',
      mergeFile: '/merge-file',
      unmergeFile: '/unmerge',
      child: '/child',
      beneficiary: '/beneficiary',
      custodianChange: '/custodian-change',
      inwardDeLink: 'inward-de-link',
      partFile: '/part-file',
      createPartFile: '/create-part-file',
      viewPartFile: '/view-part-file',
      partFileDetails: '/part-file-details',
      subfile: '/subfile'
    };

    return Object.keys(paths).some((key) => {
      return location.pathname.includes(paths[key]) && type === key;
    });
  };

  const handlePreviousRoute = () => {
    if (isAuditor) {
      routeRedirect('ui/home/<USER>/application');
    } else if (location.key) {
      window.history.back();
    } else {
      navigate('/');
    }
  };

  // const handleSummary = () => {
  //   setMergedBackButton(true);
  //   fetchFileDetails(params.fileNo);
  // };

  const hyphenStyle = (
    <div className="px-2" style={{ color: dark, fontSize: '21px' }}>
      /
    </div>
  );

  const breadItemBack = {
    isRouteBackAction: true,
    onClick: () => handlePreviousRoute()
  };

  const breadItemDash = {
    text: 'Inbox',
    onClick: () => {
      routeRedirect('ui/home/<USER>/dashboard/files');
    }
  };

  // const breadItemSummary = {
  //   text: 'Summary',
  //   rightIcon: hyphenStyle,
  //   leftIcon: (
  //     <Summary
  //       width="24px"
  //       height="24px"
  //       color={activeColor(isActiveByLocation('summary'))}
  //     />
  //   ),
  //   color: activeColor(isActiveByLocation('summary')),
  //   onClick: () => handleSummary()
  // };

  const breadItemInbox = {
    text: 'Inbox',
    onClick: () => {
      routeRedirect('ui/home/<USER>/dashboard/files');
    }
  };

  const breadItemsNoteWithFileNumber = {
    text: (
      <span className="text-[15px]">
        File No. <b className="text-[#456C86]">{fileDetails?.fileName || fileDetails?.fileNo}</b>
      </span>
    ),
    onClick: () => {
      const baseUrl = `${BASE_PATH}/file/${params?.fileNo}/notes?show=1`;
      const paramsValuesCopy = new URLSearchParams(searchParams);
      paramsValuesCopy.delete('show');
      const finalUrl = paramsValuesCopy.toString()
        ? `${baseUrl}&${paramsValuesCopy}`
        : baseUrl;
      navigate(finalUrl);
    }
  };

  const breadItemsNoteWithFileNumberAndDocumnet = {
    text: (
      <span className="text-[15px]">
        File No. <b className="text-[#456C86]">{fileDetails?.fileName || fileDetails?.fileNo}</b>
      </span>
    ),
    onClick: () => {
      const baseUrl = `${BASE_PATH}/file/${params?.fileNo}/notes?show=0`;
      const paramsValuesCopy = new URLSearchParams(searchParams);
      paramsValuesCopy.delete('show');
      const finalUrl = paramsValuesCopy.toString()
        ? `${baseUrl}&${paramsValuesCopy}`
        : baseUrl;
      navigate(finalUrl);
    }
  };

  const breadItemsNote = {
    text: (
      <span className="text-[15px] font-normal">
        File No. <b className="text-[#456C86]">{fileDetails?.fileName || fileDetails?.fileNo}</b>
      </span>
    )
  };

  const breadItemsDraft = { text: 'New Draft' };

  const breadItemFileLink = {
    text: 'Link File',
    rightIcon: hyphenStyle,
    leftIcon: (
      <LinkIcon
        width="24px"
        height="24px"
        color={activeColor(isActiveByLocation('filelink'))}
      />
    ),
    color: activeColor(isActiveByLocation('filelink')),
    onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/file-link`)
  };

  const breadItemFileUnLink = {
    text: 'UnLink File',
    rightIcon: hyphenStyle,
    leftIcon: (
      <UnLink
        width="24px"
        height="24px"
        color={activeColor(isActiveByLocation('fileUnLink'))}
      />
    ),
    color: activeColor(isActiveByLocation('fileUnlink')),
    onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/un-link-file`)
  };

  const breadItemMergeFile = {
    text: 'Merge File',
    rightIcon: hyphenStyle,
    leftIcon: (
      <Merge
        width="24px"
        height="24px"
        color={activeColor(isActiveByLocation('mergeFile'))}
      />
    ),
    color: activeColor(isActiveByLocation('mergeFile')),
    onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/merge-file`)
  };

  const breadItemUnMergeFile = {
    text: 'UnMerge File',
    rightIcon: hyphenStyle,
    leftIcon: (
      <UnMerge
        width="24px"
        height="24px"
        color={activeColor(isActiveByLocation('unmergeFile'))}
      />
    ),
    color: activeColor(isActiveByLocation('unmergeFile')),
    onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/unmerge`)
  };

  const breadItemChild = {
    text: 'Child File',
    rightIcon: hyphenStyle,
    leftIcon: (
      <UnMerge
        width="24px"
        height="24px"
        color={activeColor(isActiveByLocation('child'))}
      />
    ),
    color: activeColor(isActiveByLocation('child')),
    onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/child`)
  };

  const breadItemBeneficiary = {
    text: 'Beneficiary',
    rightIcon: hyphenStyle,
    leftIcon: (
      <Beneficiary
        width="24px"
        height="24px"
        color={activeColor(isActiveByLocation('beneficiary'))}
      />
    ),
    color: activeColor(isActiveByLocation('beneficiary')),
    onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/beneficiary`)
  };

  const breadItemCustodian = {
    text: 'Custodian Change',
    rightIcon: hyphenStyle,
    leftIcon: (
      <Beneficiary
        width="24px"
        height="24px"
        color={activeColor(isActiveByLocation('custodianChange'))}
      />
    ),
    color: activeColor(isActiveByLocation('custodianChange')),
    onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/custodian-change`)
  };

  const breadItemInwardDeLink = {
    text: 'Inward De-Link',
    rightIcon: hyphenStyle,
    leftIcon: (
      <Beneficiary
        width="24px"
        height="24px"
        color={activeColor(isActiveByLocation('inwardDeLink'))}
      />
    ),
    color: activeColor(isActiveByLocation('inwardDeLink')),
    onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/inward-de-link`)
  };

  const breadItemPartFileList = {
    text: 'Part File',
    rightIcon: hyphenStyle,
    leftIcon: (
      <PartFileIcon
        width="24px"
        height="24px"
        color={activeColor(isActiveByLocation('partFile'))}
      />
    ),
    color: activeColor(isActiveByLocation('partFile')),
    onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/part-file/partial`)
  };

  const breadItemPartFileCreate = {
    text: 'Create',
    rightIcon: hyphenStyle,
    color: activeColor(isActiveByLocation('createPartFile')),
    onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/create-part-file`)
  };

  const breadItemPartFileView = {
    text: 'View',
    rightIcon: hyphenStyle,
    color: activeColor(isActiveByLocation('viewPartFile')),
    onClick: () => navigate(
      `${BASE_PATH}/file/${params?.fileNo}/view-part-file/${params?.batchNo}`
    )
  };

  const breadItemPartFileNote = {
    text: 'Part File Note',
    rightIcon: hyphenStyle,
    color: activeColor(isActiveByLocation('partFileDetails')),
    onClick: () => navigate(
      `${BASE_PATH}/file/${params?.fileNo}/part-file-details/${params?.partFileNo}`
    )
  };

  const breadItemSub = {
    text: 'Sub File',
    rightIcon: hyphenStyle,
    leftIcon: (
      <UnMerge
        width="24px"
        height="24px"
        color={activeColor(isActiveByLocation('child'))}
      />
    ),
    color: activeColor(isActiveByLocation('child')),
    onClick: () => navigate(`${BASE_PATH}/file/${params?.fileNo}/subfile`)
  };

  const fileBreadCrumb = () => {
    const breadCrumbMapping = {
      '/summary': [breadItemBack, breadItemDash, breadItemsNoteWithFileNumberAndDocumnet],
      '/notes': [breadItemBack, breadItemInbox, breadItemsNote],
      '/draft': [
        breadItemBack,
        breadItemInbox,
        breadItemsNoteWithFileNumber,
        breadItemsDraft
      ],
      '/file-link': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemFileLink
      ],
      '/un-link-file': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemFileUnLink
      ],
      '/merge-file': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemMergeFile
      ],
      '/unmerge': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemUnMergeFile
      ],
      '/child': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemChild
      ],
      '/beneficiary': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemBeneficiary
      ],
      '/custodian-change': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemCustodian
      ],
      '/inward-de-link': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemInwardDeLink
      ],
      '/part-file-details': [
        breadItemBack,
        breadItemDash,
        breadItemPartFileNote
      ],
      '/part-file': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemPartFileList
      ],
      '/create-part-file': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemPartFileList,
        breadItemPartFileCreate
      ],
      '/view-part-file': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemPartFileList,
        breadItemPartFileView
      ],
      '/subfile': [
        breadItemBack,
        breadItemDash,
        breadItemsNoteWithFileNumberAndDocumnet,
        breadItemSub
      ]
    };
    const matchedKey = Object.keys(breadCrumbMapping).find((key) => location.pathname.includes(key));
    setBreadItems(
      matchedKey
        ? breadCrumbMapping[matchedKey]
        : [breadItemBack, breadItemDash, breadItemsNoteWithFileNumberAndDocumnet]
    );
  };

  useEffect(() => {
    fileBreadCrumb();
  }, [location?.pathname, fileDetails?.url]);

  useEffect(() => {
    if (pen) fetchPostIdByPenNo({ penNo: pen });
  }, [pen]);

  const renderFeaturesMenu = () => {
    return (
      <GeneralFeatureWithSearch
        menu={featureMenu(fileDetails?.status)}
        handleSubmit={submit}
        isDisabled={generalFeatureMenu(
          fileDetails?.status,
          fileDetails?.stage,
          fileDetails?.role,
          searchParams.get('flowaction'),
          postIdByPenNoDetails,
          fileDetails?.postId,
          fileDetails?.moduleCode,
          location.pathname.includes(`${BASE_PATH}/part-file`),
          fileDetails?.source,
          fileDetails?.fileName
        )}
      />
    );
  };

  return (
    <div
      style={{ backgroundColor: light }}
      className="h-screen overflow-auto font-body"
    >
      <MainHeader />

      <div className="fixed w-full top-[70px] z-40 border-t shadow-md bg-white">
        <FileHeader
          breadItems={breadItems}
          fileDetails={fileDetails}
          isSummaryPage={location.pathname.includes('/summary')}
          isShowFeaturesMenu={
            (location.pathname.includes(`${BASE_PATH}/file`)
              || location.pathname.includes(`${BASE_PATH}/part-file`))
            && !location.pathname.includes('/part-file-details')
          }
          renderFeaturesMenuTemp={renderFeaturesMenu}
        />
        {location.pathname.includes('/summary') && <FileHead />}
      </div>

      <div
        className={
          location.pathname.includes('/summary')
            ? 'pl-[30px] pr-[30px] pt-[215px]'
            : 'pl-[30px] pr-[30px] pt-[144px]'
        }
        style={{ height: '100%' }}
      >
        <Outlet />
        <ksmart-widget applicationId="ksmart-floating-widget" />
      </div>
      {interpellationPop ? <Interpellation /> : null}
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  fileDetails: getFileDetails,
  userInfo: getUserInfo,
  postIdByPenNoDetails: getPostIdByPenNoDetails,
  mergeLinkFiles: getMergeLinkFiles,
  summaryButtonFlag: getSummaryBackButton,
  interpellationPop: getInterpellationPopupOpen
});

const mapDispatchToProps = (dispatch) => ({
  fetchPostIdByPenNo: (data) => dispatch(commonActions.fetchPostIdByPenNo(data)),
  setMergedBackButton: (data) => dispatch(sliceActions.setMergedBackButton(data)),
  setSummaryBackButton: (data) => dispatch(sliceActions.setSummaryBackButton(data)),
  fetchFileDetails: (data) => dispatch(actions.fetchFileDetails(data)),
  fetchMergeLink: (data) => dispatch(actions.fetchMergeLink(data)),
  setInterpellationPop: (data) => dispatch(detailsActions.setInterpellationPopup(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(File);
