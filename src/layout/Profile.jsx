import React from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import MainHeader from 'common/components/MainHeader';
import { Button } from '@ksmartikm/ui-components';
import BackArrow from 'assets/BackIcon';
import { dark } from 'utils/color';
import { BASE_UI_PATH } from 'common/constants';
import { t } from 'i18next';

const Profile = () => {
  const hyphenStyle = <div className="px-2" style={{ color: dark, fontSize: '21px' }}>/</div>;
  const navigate = useNavigate();
  const location = useLocation();
  const backToProfile = () => {
    if (location?.pathname.includes('deactivated')) {
      navigate('ds/enroll');
    } else {
      window.location.href = `${BASE_UI_PATH}home/employee/my-profile`;
    }
  };

  const handleDeactivatedList = () => {
    navigate('ds/deactivated');
  };

  return (
    <div className="w-screen h-screen">
      <MainHeader />
      <div className="flex mt-20 gap-4 px-20 py-2">
        <Button style={{ paddingLeft: 0, paddingRight: 0 }} variant="ghost" leftIcon={<BackArrow width="40px" height="40px" color={dark} />} onClick={backToProfile} rightIcon={hyphenStyle}>
          <h4 className="flex-grow font-semibold text-[21px]" style={{ color: dark }}>{t('profile')}</h4>
        </Button>
        <Button style={{ paddingLeft: 0, paddingRight: 0 }} variant="ghost">
          <h4 className="flex-grow font-semibold text-[21px]" style={{ color: dark }}>{t('digitalSignature')}</h4>
        </Button>
        <div className="flex-grow" />
        {!location?.pathname.includes('deactivated')
        && (
        <div>
          <Button variant="primary_outline" size="xs" onClick={handleDeactivatedList}>
            {t('deactivatedList')}
          </Button>
        </div>
        )}
      </div>
      <div
        className="flex grid-flow-col auto-cols-max gap-4 px-20 pb-4"
        style={{ background: '#E7EFF5' }}
      >
        {/* <div className="flex-none bg-white border rounded-lg p-3 mt-20" style={{ height: 'calc(100vh - 180px)' }}>
          <Button>Digita

          l Signature</Button>
        </div> */}

        <div className="grow">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default Profile;
