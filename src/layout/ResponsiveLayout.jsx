/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useEffect, useState } from 'react';
import { createStructuredSelector } from 'reselect';
import { connect, useSelector } from 'react-redux';
import { Outlet, useLocation, useSearchParams } from 'react-router-dom';
import { dark, light, secondary } from 'utils/color';
import MainHeader from 'common/components/MainHeader';
import { actions as commonSliceActions } from 'pages/common/slice';
import FormTitle from 'common/components/FormTitle';
import { STATE_REDUCER_KEY } from 'pages/common';
import BackArrow from 'assets/BackIcon';
import { Button, IconButton } from '@ksmartikm/ui-components';
import { CITIZEN_APPLICATION_PATH, CITIZEN_SERVICE_PATH, EMPLOYEE_SERVICE_PATH } from 'common/constants';
import { getEFilePreview } from 'pages/citizen/e-file/selectors';
import { FILE_STATUS_FOR_API_PARAMS } from 'pages/common/constants';
import { t } from 'i18next';
import InfoSolid from 'assets/InfoSolid';
import { KSWIFT_HOME_URL } from 'pages/kswift/login/constants';
import { Body, Sidebar } from './components';

const ResponsiveLayout = ({
  eFilePreview,
  setAlertAction
}) => {
  const {
    layout: { formTitle: { title = 'welcome', variant = 'normal' } } = {},
    sidebarData: { activeStep }
  } = useSelector((state) => state[STATE_REDUCER_KEY]);

  const location = useLocation();
  const [noteTrigger, setNoteTrigger] = useState(false);
  const [searchParams] = useSearchParams();
  const [isOpen, setIsOpen] = useState(false);
  const [isMdScreen, setIsMdScreen] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      if (window.innerWidth >= 768) {
        setIsOpen(true);
        setIsMdScreen(true);
      } else {
        setIsOpen(false);
        setIsMdScreen(false);
      }
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  const handleOpen = () => {
    setAlertAction({
      open: true, title: eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.REJECTED ? t('reasonForRejection') : t('reasonForReturn'), message: eFilePreview?.lastNote, variant: 'alert', backwardActionText: t('ok')
    });
  };

  // const backToHome = () => {
  //   if (location.pathname.includes('citizen/e-file')) {
  //     if (searchParams?.get('kswiftId')) {
  //       localStorage.clear();
  //       window.location.href = KSWIFT_HOME_URL;
  //     } else {
  //       window.location.href = CITIZEN_SERVICE_PATH;
  //     }
  //   } else {
  //     window.location.href = EMPLOYEE_SERVICE_PATH;
  //   }
  // };

  const backToHome = () => {
    if (!location.pathname.includes('citizen/e-file')) {
      window.location.href = EMPLOYEE_SERVICE_PATH;
      return;
    }

    if (searchParams?.get('kswiftId')) {
      localStorage.clear();
      window.location.href = KSWIFT_HOME_URL;
      return;
    }

    // Paths with two segments (like citizen/e-file/ANY_CODE/ANY_ID)
    if (location.pathname.match(/\/citizen\/e-file\/[^/]+\/[^/]+$/)) {
      window.location.href = CITIZEN_APPLICATION_PATH;
      return;
    }

    // Paths with single segment (like citizen/e-file/ANY_CODE) or any other case
    window.location.href = CITIZEN_SERVICE_PATH;
  };

  const offset = 5;

  const handleScroll = (e, top) => {
    window.scrollTo({
      top: (Number(e) * 60) + top,
      behavior: 'smooth'
    });
  };

  useEffect(() => {
    if (activeStep) {
      setTimeout(() => {
        handleScroll(activeStep, offset);
      }, 100);
    }
  }, [activeStep]);

  useEffect(() => {
    if ((eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.RETURN_TO_CITIZEN && eFilePreview?.lastNote && !noteTrigger) || (eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.REJECTED && eFilePreview?.lastNote && !noteTrigger)) {
      setAlertAction({
        open: true, title: eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.REJECTED ? t('reasonForRejection') : t('reasonForReturn'), message: eFilePreview?.lastNote, variant: 'alert', backwardActionText: t('ok')
      });
      setNoteTrigger(true);
    }
  }, [eFilePreview]);

  return (
    <div style={{ backgroundColor: light }} className="font-body">
      <MainHeader />
      <div className="fixed w-full top-[65px] px-[20px] md:px-[80px] z-50 pb-3" style={{ backgroundColor: light }}>
        <div className="flex items-center gap-3 mt-5 pb-1">
          <div className="flex-none">
            <IconButton
              onClick={backToHome}
              variant="ghost"
              icon={(
                <BackArrow
                  color={dark}
                  width={isMdScreen ? '12' : '8'}
                  height={isMdScreen ? '12' : '8'}
                />
)}
            />
          </div>
          <div className="flex-grow">
            <FormTitle title={title} variant={variant} />
          </div>
          {(eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.RETURN_TO_CITIZEN || eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.REJECTED) && (
            <div className="flex-none">
              <Button variant="ghost" onClick={handleOpen} leftIcon={<InfoSolid color={secondary} />}>
                {eFilePreview?.stage === FILE_STATUS_FOR_API_PARAMS.REJECTED ? t('reasonForRejection') : t('reasonForReturn')}
              </Button>
            </div>
          )}
        </div>
      </div>
      <div className="md:px-[80px] pt-[140px] sm:px-[20px]">
        <div className="col-span-12">

          <div className="flex auto-cols-max gap-5">
            <div className={`flex-none ${isMdScreen ? 'w-[250px]' : 'w-[0px]'}`}>

              <div className="relative flex items-center">
                {/* Toggle Button (Always Visible) */}
                {!isMdScreen && (
                <button
                  onClick={() => setIsOpen(!isOpen)}
                  className="fixed top-1/2 left-0 transform -translate-y-1/2 bg-gray-200 hover:bg-gray-300 transition-all
                   w-10 h-10 justify-center items-center rounded-r-md shadow-md z-[101] md:left-20"
                >
                  <span className="text-lg font-bold">{isOpen ? '<<' : '>>'}</span>
                </button>
                )}
                {/* Sidebar (Hidden by Default, Opens on Click) */}
                {isOpen && (
                <div
                  onClick={() => {
                    if (!isMdScreen) setIsOpen(false);
                  }}
                  className={`fixed top-[142px] left-0 w-[250px] md:left-[80px] z-[100] bg-white shadow-lg transition-transform duration-300 ${isOpen ? 'translate-x-0' : '-translate-x-full'} md:block`}
                >
                  <Sidebar />
                </div>
                )}
              </div>
              {/* <div className="fixed w-[250px] left-[80px]">
                <Sidebar />
              </div> */}
            </div>
            <div className={`${isMdScreen ? 'grow ml-[20px]' : 'w-full'} mt-1`}>
              <Body>
                <Outlet />
              </Body>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const mapStateToProps = createStructuredSelector({
  eFilePreview: getEFilePreview
});

const mapDispatchToProps = (dispatch) => ({
  setAlertAction: (data) => dispatch(commonSliceActions.setAlertAction(data))
});

export default connect(mapStateToProps, mapDispatchToProps)(ResponsiveLayout);
