import React from 'react';

const AddCircle = ({
  width, height, color = '#E82C78', ...rest
}) => (
  <svg width={width} height={height} viewBox="0 0 31 31" fill="none" xmlns="http://www.w3.org/2000/svg" {...rest || {}}>
    <g id="vuesax/outline/add-circle">
      <g id="add-circle">
        <path id="Vector" d="M15.4999 29.3856C7.84034 29.3856 1.6145 23.1597 1.6145 15.5002C1.6145 7.84058 7.84034 1.61475 15.4999 1.61475C23.1595 1.61475 29.3853 7.84058 29.3853 15.5002C29.3853 23.1597 23.1595 29.3856 15.4999 29.3856ZM15.4999 3.55225C8.91242 3.55225 3.552 8.91266 3.552 15.5002C3.552 22.0877 8.91242 27.4481 15.4999 27.4481C22.0874 27.4481 27.4478 22.0877 27.4478 15.5002C27.4478 8.91266 22.0874 3.55225 15.4999 3.55225Z" fill={color} />
        <path id="Vector_2" d="M20.6666 16.4688H10.3333C9.80367 16.4688 9.3645 16.0296 9.3645 15.5C9.3645 14.9704 9.80367 14.5312 10.3333 14.5312H20.6666C21.1962 14.5312 21.6353 14.9704 21.6353 15.5C21.6353 16.0296 21.1962 16.4688 20.6666 16.4688Z" fill={color} />
        <path id="Vector_3" d="M15.5 21.6356C14.9704 21.6356 14.5312 21.1964 14.5312 20.6668V10.3335C14.5312 9.80391 14.9704 9.36475 15.5 9.36475C16.0296 9.36475 16.4688 9.80391 16.4688 10.3335V20.6668C16.4688 21.1964 16.0296 21.6356 15.5 21.6356Z" fill={color} />
      </g>
    </g>
  </svg>
);

export default AddCircle;
