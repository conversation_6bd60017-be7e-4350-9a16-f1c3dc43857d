const SummaryUserSquareIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M14.3574 15.2759L14.0924 14.6109C13.9583 14.2734 13.7508 13.9709 13.4841 13.7242L13.4358 13.6792C13.0166 13.2909 12.4666 13.0742 11.8949 13.0742H8.10327C7.5316 13.0742 6.98077 13.2909 6.5616 13.6784L6.51327 13.7234C6.24743 13.9701 6.03993 14.2726 5.90493 14.6101L5.6416 15.2759"
        stroke="#456C86"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.8712 6.6093C12.9045 7.64263 12.9045 9.31846 11.8712 10.3526C10.8379 11.3868 9.16202 11.386 8.12785 10.3526C7.09368 9.3193 7.09452 7.64346 8.12785 6.6093C9.16118 5.57513 10.837 5.57596 11.8712 6.6093"
        stroke="#456C86"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.8333 17.5H4.16667C3.24583 17.5 2.5 16.7542 2.5 15.8333V4.16667C2.5 3.24583 3.24583 2.5 4.16667 2.5H15.8333C16.7542 2.5 17.5 3.24583 17.5 4.16667V15.8333C17.5 16.7542 16.7542 17.5 15.8333 17.5Z"
        stroke="#456C86"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default SummaryUserSquareIcon;
