import React from 'react';

const Claim = () => (

  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M21 16V18C21 19.6569 19.6569 21 18 21H17" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M3 9V6C3 4.34315 4.34315 3 6 3H8" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M3 20L4.156 20.578C4.71109 20.8555 5.32318 21 5.94378 21H12.5C13.3284 21 14 20.3284 14 19.5V19.5C14 18.6716 13.3284 18 12.5 18H8.5" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M3 13H5.91335C6.34609 13 6.76716 13.1404 7.11335 13.4L9.35062 15.0779C9.7271 15.3603 9.96252 15.7921 9.99588 16.2615C10.0292 16.731 9.85726 17.1917 9.5245 17.5245V17.5245C8.95325 18.0958 8.04889 18.16 7.4026 17.6753L6 16.6233" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M21 9.73187V10.9819C21 12.0864 20.1046 12.9819 19 12.9819H13C11.8954 12.9819 11 12.0864 11 10.9819V4.98187C11 3.8773 11.8954 2.98187 13 2.98187H19C20.1046 2.98187 21 3.8773 21 4.98187V6.23187" fill="#FFE1EC" />
    <path d="M21 9.73187V10.9819C21 12.0864 20.1046 12.9819 19 12.9819H13C11.8954 12.9819 11 12.0864 11 10.9819V4.98187C11 3.8773 11.8954 2.98187 13 2.98187H19C20.1046 2.98187 21 3.8773 21 4.98187V6.23187" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M20.25 6.23187H21.5C21.7761 6.23187 22 6.45573 22 6.73187V9.23187C22 9.50801 21.7761 9.73187 21.5 9.73187H20.25C19.2835 9.73187 18.5 8.94837 18.5 7.98187V7.98187C18.5 7.01537 19.2835 6.23187 20.25 6.23187Z" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default Claim;
