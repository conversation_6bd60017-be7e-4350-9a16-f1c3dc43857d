import React from 'react';

const Receipt = () => (

  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.7443 6.56689C13.1585 6.56689 13.4943 6.23111 13.4943 5.81689C13.4943 5.40268 13.1585 5.06689 12.7443 5.06689V6.56689ZM8.09375 17.2005V6.2083H6.59375V17.2005H8.09375ZM8.09375 6.2083C8.09375 6.40594 7.9328 6.56689 7.73516 6.56689V5.06689C7.10437 5.06689 6.59375 5.57752 6.59375 6.2083H8.09375ZM7.73516 6.56689H12.7443V5.06689H7.73516V6.56689Z" fill="#09327B" />
    <path d="M18.9394 12.0923V18.595C18.9394 19.8008 17.9524 20.7878 16.7466 20.7878H16.6947C15.4889 20.7878 14.502 19.8008 14.502 18.595V16.9854" stroke="#09327B" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" />
    <path d="M9.79433 14.0737C10.2071 14.0737 10.5418 13.739 10.5418 13.3262C10.5418 12.9134 10.2071 12.5787 9.79433 12.5787C9.38152 12.5787 9.04688 12.9134 9.04688 13.3262C9.04688 13.739 9.38152 14.0737 9.79433 14.0737Z" fill="#09327B" />
    <path d="M9.79433 11.5906C10.2071 11.5906 10.5418 11.256 10.5418 10.8432C10.5418 10.4304 10.2071 10.0957 9.79433 10.0957C9.38152 10.0957 9.04688 10.4304 9.04688 10.8432C9.04688 11.256 9.38152 11.5906 9.79433 11.5906Z" fill="#09327B" />
    <line x1="11.9238" y1="13.2539" x2="14.7262" y2="13.2539" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" />
    <line x1="11.9238" y1="10.7842" x2="14.7262" y2="10.7842" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" />
    <path d="M19.2547 3.2124H15.125H15.485C16.7039 3.2124 17.692 4.20051 17.692 5.41941V5.41941C17.692 6.6383 16.7039 7.62641 15.485 7.62641H15.3221C15.2506 7.62641 15.2155 7.71349 15.2671 7.76305L17.692 10.0953M15.125 5.27125H16.7628H18.8594" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M13.7756 17.7349H14.5256V16.2349H13.7756V17.7349ZM7.89985 20.7894V21.5394V20.7894ZM5.39687 17.7349H13.7756V16.2349H5.39687V17.7349ZM16.5671 20.0394L7.89985 20.0394V21.5394H16.5671V20.0394ZM7.89985 20.0394C6.57229 20.0394 5.49609 18.9632 5.49609 17.6356H3.99609C3.99609 19.7916 5.74387 21.5394 7.89985 21.5394V20.0394ZM5.39687 16.2349C4.62324 16.2349 3.99609 16.862 3.99609 17.6356H5.49609C5.49609 17.6904 5.45167 17.7349 5.39687 17.7349V16.2349Z" fill="#09327B" />
  </svg>

);

export default Receipt;
