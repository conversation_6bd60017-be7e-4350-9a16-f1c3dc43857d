import React from 'react';

const Note = () => (

  <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" clipRule="evenodd" d="M14.376 20.02C14.2096 20.1864 13.984 20.2799 13.7487 20.2799H11.9609V18.4921C11.9609 18.2568 12.0545 18.0312 12.2209 17.8648L18.2266 11.8544C18.513 11.568 18.9014 11.4071 19.3064 11.4071C19.7114 11.4071 20.0998 11.568 20.3861 11.8544C20.6727 12.1407 20.8338 12.5291 20.8338 12.9342C20.8338 13.3393 20.6727 13.7278 20.3861 14.014L14.376 20.02Z" fill="#FFE1EC" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M16.2305 13.8922L18.3595 16.0213" stroke="#E72F77" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M20.0018 14.7349L20.5375 15.3751C20.9323 15.8332 20.9323 16.575 20.5375 17.0331L19.1699 18.6168" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.47852 7.9787H17.4142" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.61523 12.0001L14.9487 12.0001" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7.47852 16.0215H10.3765" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.68584 20.2796H6.92593C5.40167 20.2796 4.16602 19.0439 4.16602 17.5197V6.48001C4.16602 4.95575 5.40167 3.72009 6.92593 3.72009H17.9656C19.4898 3.72009 20.7255 4.95575 20.7255 6.48001V8.31995" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default Note;
