import React from 'react';

const AgendaIcon = ({ w = '32', h = '32' }) => (

  <svg width={w} height={h} viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15.4375 14.9066L9.50798 14.9066C8.41006 14.9066 7.52002 15.8219 7.52002 16.9509L7.52002 25.1279M24.7491 25.1279L24.7491 16.2695" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M3.78223 26.1276H28.2178" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M15.9526 14.9072L15.9526 11.2367C15.9526 10.7174 16.0735 10.2052 16.3058 9.74073L16.632 9.08821C16.9166 8.51918 17.3574 8.04308 17.903 7.71577L22.6429 4.87179" stroke="#E82C78" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M28.2179 10.4439L27.0381 13.5118C26.6907 14.4154 26.0587 15.1814 25.2376 15.6942L19.7939 19.0937C19.0901 19.5331 18.1761 19.4289 17.5894 18.8422V18.8422C17.2435 18.4962 17.0549 18.0233 17.068 17.5342C17.0811 17.0451 17.2946 16.583 17.6585 16.256L20.4125 13.7815L20.4125 11.5589" stroke="#E82C78" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default AgendaIcon;
