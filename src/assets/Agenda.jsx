import React from 'react';

const Agenda = () => (

  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M7 3.00003H5C3.895 3.00003 3 3.89503 3 5.00003V19C3 20.105 3.895 21 5 21H17C18.105 21 19 20.105 19 19V18" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M13 17L16.15 16.611C16.371 16.584 16.577 16.483 16.735 16.326L21.366 11.695C22.211 10.85 22.211 9.48002 21.366 8.63402V8.63402C20.521 7.78902 19.151 7.78902 18.305 8.63402L13.745 13.194C13.592 13.347 13.493 13.545 13.462 13.76L13 17Z" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M13.5 4.50003H8.5C7.672 4.50003 7 3.82803 7 3.00003V3.00003C7 2.17203 7.672 1.50003 8.5 1.50003H13.5C14.328 1.50003 15 2.17203 15 3.00003V3.00003C15 3.82803 14.328 4.50003 13.5 4.50003Z" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7 8.00003H13" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M7 12H11" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M15 3.00003H17C18.105 3.00003 19 3.89503 19 5.00003" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default Agenda;
