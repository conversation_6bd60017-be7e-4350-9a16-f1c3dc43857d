import { Icon } from '@ksmartikm/ui-components';

const Download = (props) => {
  return (
    <Icon xmlns="http://www.w3.org/2000/svg" width="5" height="5" viewBox="0 0 20 20" fill="none" {...props}>
      <g clipPath="url(#clip0_2614_66963)">
        <path d="M3.33203 14.1665V15.8332C3.33203 16.2752 3.50763 16.6991 3.82019 17.0117C4.13275 17.3242 4.55667 17.4998 4.9987 17.4998H14.9987C15.4407 17.4998 15.8646 17.3242 16.1772 17.0117C16.4898 16.6991 16.6654 16.2752 16.6654 15.8332V14.1665" stroke="#00B2EC" strokeWidth="1.7" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M5.83203 9.1665L9.9987 13.3332L14.1654 9.1665" stroke="#00B2EC" strokeWidth="1.7" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M10 3.3335V13.3335" stroke="#00B2EC" strokeWidth="1.7" strokeLinecap="round" strokeLinejoin="round" />
      </g>
      <defs>
        <clipPath id="clip0_2614_66963">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </Icon>
  );
};
export default Download;
