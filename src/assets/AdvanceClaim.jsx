import React from 'react';

const AdvanceClaim = () => (

  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.0032 9.99913V4.99705C20.0032 3.89202 19.1074 2.99622 18.0023 2.99622H4.99693C3.8919 2.99622 2.99609 3.89202 2.99609 4.99705V19.0029C2.99609 20.1079 3.8919 21.0037 4.99693 21.0037H9.99901" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.2481 15.0012H9.99841" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.24805 10.9996H14" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M9.24805 7.12301H16.0009" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.1213 15.1262C6.19037 15.1262 6.24635 15.0702 6.24635 15.0012C6.24635 14.9321 6.19037 14.8761 6.1213 14.8761C6.05224 14.8761 5.99625 14.9321 5.99625 15.0012C5.99625 15.0702 6.05224 15.1262 6.1213 15.1262" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.1213 11.1247C6.19037 11.1247 6.24635 11.0687 6.24635 10.9996C6.24635 10.9305 6.19037 10.8745 6.1213 10.8745C6.05224 10.8745 5.99625 10.9305 5.99625 10.9996C5.99625 11.0687 6.05224 11.1247 6.1213 11.1247" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M6.1213 7.24806C6.19037 7.24806 6.24635 7.19208 6.24635 7.12301C6.24635 7.05395 6.19037 6.99796 6.1213 6.99796C6.05224 6.99796 5.99625 7.05395 5.99625 7.12301C5.99625 7.19208 6.05224 7.24806 6.1213 7.24806" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M20 20.65V18.35C20 17.6044 19.3956 17 18.65 17H14.35C13.6044 17 13 17.6044 13 18.35V20.65C13 21.3956 13.6044 22 14.35 22H18.65C19.3956 22 20 21.3956 20 20.65Z" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M16 17.0407L16.5383 14.9816C16.6253 14.6427 16.839 14.3535 17.1323 14.1778C17.4257 14.0021 17.7746 13.9542 18.1021 14.0449L22.0499 15.1407C22.3787 15.2317 22.659 15.4543 22.8289 15.7594C22.9988 16.0645 23.0444 16.4269 22.9556 16.7668L22.3062 19.2766C22.1689 19.7984 21.649 20.1068 21.1441 19.9659L20.2469 19.7184" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default AdvanceClaim;
