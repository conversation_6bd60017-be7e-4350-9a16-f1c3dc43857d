const ChildFileIcon = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_4455_80191)">
        <path
          d="M6.75 12.75H3.75C2.92157 12.75 2.25 12.0784 2.25 11.25V2.25"
          stroke="#09327B"
          strokeWidth="1.125"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M6.75 5.25H2.25"
          stroke="#09327B"
          strokeWidth="1.125"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.75 6.375V3.75C15.75 3.12868 15.2463 2.625 14.625 2.625H13.0786C12.8303 2.625 12.5981 2.50212 12.4585 2.29682L12.1397 1.82818C12.0001 1.62287 11.7679 1.49999 11.5196 1.5H10.125C9.50368 1.5 9 2.00368 9 2.625V6.375C9 6.99632 9.50368 7.5 10.125 7.5H14.625C15.2463 7.5 15.75 6.99632 15.75 6.375Z"
          stroke="#09327B"
          strokeWidth="1.125"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.75 14.625V12C15.75 11.3787 15.2463 10.875 14.625 10.875H13.0786C12.8303 10.875 12.5981 10.7521 12.4585 10.5468L12.1397 10.0782C12.0001 9.87287 11.7679 9.74999 11.5196 9.75H10.125C9.50368 9.75 9 10.2537 9 10.875V14.625C9 15.2463 9.50368 15.75 10.125 15.75H14.625C15.2463 15.75 15.75 15.2463 15.75 14.625Z"
          stroke="#E82C78"
          strokeWidth="1.125"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_4455_80191">
          <rect width="18" height="18" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default ChildFileIcon;
