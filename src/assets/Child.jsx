import React from 'react';

const Child = () => (

  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clipPath="url(#clip0_3528_29439)">
      <path d="M9 17H5C3.89543 17 3 16.1046 3 15V3.00003" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M9 7.00003H3" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path fillRule="evenodd" clipRule="evenodd" d="M21 8.50003V5.00003C21 4.1716 20.3284 3.50003 19.5 3.50003H17.4381C17.1071 3.50003 16.7975 3.33619 16.6113 3.06246L16.1863 2.4376C16.0001 2.16386 15.6905 2.00002 15.3594 2.00003H13.5C12.6716 2.00003 12 2.6716 12 3.50003V8.50003C12 9.32846 12.6716 10 13.5 10H19.5C20.3284 10 21 9.32846 21 8.50003Z" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path fillRule="evenodd" clipRule="evenodd" d="M21 19.5V16C21 15.1716 20.3284 14.5 19.5 14.5H17.4381C17.1071 14.5 16.7975 14.3362 16.6113 14.0625L16.1863 13.4376C16.0001 13.1639 15.6905 13 15.3594 13H13.5C12.6716 13 12 13.6716 12 14.5V19.5C12 20.3285 12.6716 21 13.5 21H19.5C20.3284 21 21 20.3285 21 19.5Z" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </g>
    <defs>
      <clipPath id="clip0_3528_29439">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>

);

export default Child;
