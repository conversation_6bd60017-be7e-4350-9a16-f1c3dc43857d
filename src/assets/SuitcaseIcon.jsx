const SuitcaseIcon = ({ fill = 'currentColor' }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="60"
      height="60"
      viewBox="0 0 14 14"
    >
      <path
        fill={fill}
        fillRule="evenodd"
        d="M6.5 1.5a.75.75 0 0 0-.75.75v.522c.405-.016.816-.022 1.25-.022s.845.006 1.25.022V2.25a.75.75 0 0 0-.75-.75zm-2.25.75v.637q-.4.045-.823.112a1.41 1.41 0 0 0-1.178 1.178C2.05 5.447 2 6.5 2 7.75c0 .************* 1.375h9.944c.02-.445.028-.896.028-1.375c0-1.249-.05-2.302-.25-3.573a1.41 1.41 0 0 0-1.177-1.178a20 20 0 0 0-.823-.112V2.25A2.25 2.25 0 0 0 7.5 0h-1a2.25 2.25 0 0 0-2.25 2.25m7.627 8.125H2.123c.04.37.093.751.16 1.155c.08.48.456.857.937.937q.27.045.53.082v.701a.75.75 0 0 0 1.5 0v-.548c.569.036 1.136.048 1.75.048s1.181-.012 1.75-.048v.548a.75.75 0 0 0 1.5 0v-.701l.323-.048a1.41 1.41 0 0 0 1.178-1.178q.077-.49.126-.948"
        clipRule="evenodd"
      />
    </svg>
  );
};

export default SuitcaseIcon;
