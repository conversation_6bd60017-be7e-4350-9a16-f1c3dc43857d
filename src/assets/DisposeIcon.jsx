const DisposeIcon = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M21 7.49242V19C21 20.1046 20.1046 21 19 21H5C3.89543 21 3 20.1046 3 19V7.49242C3 7.16537 3.04011 6.83956 3.11943 6.52228L3.62127 4.51493C3.84385 3.6246 4.64382 3 5.56155 3H18.4384C19.3562 3 20.1561 3.6246 20.3787 4.51493L20.8806 6.52228C20.9599 6.83956 21 7.16537 21 7.49242Z"
        stroke="#09327B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.5 9.5L7.5 12.1475C7.49999 13.1278 8.21055 13.9636 9.1781 14.1214L11.5 14.5"
        stroke="#E83A7A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.0619 9.5L17.2625 11.3012C17.6364 11.8622 17.5624 12.6091 17.0857 13.0858V13.0858C16.8205 13.351 16.4607 13.5 16.0856 13.5C15.7105 13.5 15.3507 13.351 15.0855 13.0857L14.5 12.5002"
        stroke="#E83A7A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.5 14.5L11.5 17C11.5 17.8284 12.1716 18.5 13 18.5C13.8284 18.5 14.5 17.8284 14.5 17L14.5 12.5"
        stroke="#E83A7A"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20.9684 7H3.03125"
        stroke="#09327B"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default DisposeIcon;
