import React from 'react';

const PendingIcon = ({ w = '24', h = '24' }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={w} height={h} viewBox="0 0 24 24" fill="none">
      <circle cx="6.5" cy="16.5" r="4.5" stroke="#DE9724" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M13 21H17.5C18.8807 21 20 19.8807 20 18.5V8.37167C20 7.57602 19.6839 6.81296 19.1213 6.25035L16.7497 3.87868C16.187 3.31607 15.424 3 14.6283 3H7.5C6.11929 3 5 4.11929 5 5.5V9" stroke="#DE9724" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M19.9764 7.99982H16.5C15.6716 7.99982 15 7.32824 15 6.49982V3.02344" stroke="#DE9724" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M6.39301 14.9834V16.737L7.77521 17.5801" stroke="#DE9724" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export default PendingIcon;
