import { Icon } from '@ksmartikm/ui-components';
import React from 'react';

const DownloadIcon = (props) => (
  <Icon viewBox="0 0 24 24" fill="none" {...props}>
    <path d="M15 10L12 13L9 10" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12 3V13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8 16H16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M18.364 5.63599C21.879 9.15099 21.879 14.849 18.364 18.364C14.849 21.879 9.15101 21.879 5.63601 18.364C2.12101 14.849 2.12101 9.15099 5.63601 5.63599" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </Icon>
);

export default DownloadIcon;
