const PersonHead = ({
  strokeWidth = '2', stroke = '#456C86', w = '16', h = '16'
}) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_3040_4435)">
        <path
          d="M7.99967 7.33382C9.47243 7.33382 10.6663 6.13991 10.6663 4.66716C10.6663 3.1944 9.47243 2.00049 7.99967 2.00049C6.52692 2.00049 5.33301 3.1944 5.33301 4.66716C5.33301 6.13991 6.52692 7.33382 7.99967 7.33382Z"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M4 14V12.6667C4 11.9594 4.28095 11.2811 4.78105 10.781C5.28115 10.281 5.95942 10 6.66667 10H9.33333C10.0406 10 10.7189 10.281 11.219 10.781C11.719 11.2811 12 11.9594 12 12.6667V14"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_3040_4435">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default PersonHead;
