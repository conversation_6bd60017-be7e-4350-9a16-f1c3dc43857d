import React from 'react';

const Beneficiary = () => (

  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M11 21H7.5C5.01472 21 3 18.9853 3 16.5V7.50003C3 5.01475 5.01472 3.00003 7.5 3.00003H16.5C18.9853 3.00003 21 5.01475 21 7.50003V11" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <circle cx="18" cy="18" r="4" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M19.25 17.9997L16.75 18.0003" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M17.9997 16.75L18.0003 19.25" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <circle cx="10" cy="9.50003" r="2" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M10 7.50003C11.1046 7.50003 12 8.39546 12 9.50003C12 10.6046 11.1046 11.5 10 11.5" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14.4844 7.50003C15.5889 7.50003 16.4844 8.39546 16.4844 9.50003C16.4844 10.6046 15.5889 11.5 14.4844 11.5" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M14.4844 7.50003C15.5889 7.50003 16.4844 8.39546 16.4844 9.50003C16.4844 10.6046 15.5889 11.5 14.4844 11.5" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M11.5 14H8.5C7.39543 14 6.5 14.8955 6.5 16" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default Beneficiary;
