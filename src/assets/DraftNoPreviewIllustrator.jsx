const DraftNoPreviewIllustrator = () => {
  return (
    <svg width="184" height="147" viewBox="0 0 184 147" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M152.352 47.7749C155.197 47.7749 157.504 50.0784 157.504 52.9199C157.504 55.7614 155.197 58.0649 152.352 58.0649H122.912C125.757 58.0649 128.064 60.3684 128.064 63.2099C128.064 66.0514 125.757 68.3549 122.912 68.3549H139.104C141.949 68.3549 144.256 70.6584 144.256 73.4999C144.256 76.3414 141.949 78.6449 139.104 78.6449H131.616C128.028 78.6449 125.12 80.9484 125.12 83.7899C125.12 85.6842 126.592 87.3992 129.536 88.9349C132.381 88.9349 134.688 91.2384 134.688 94.0799C134.688 96.9214 132.381 99.2249 129.536 99.2249H68.4478C65.6024 99.2249 63.2958 96.9214 63.2958 94.0799C63.2958 91.2384 65.6024 88.9349 68.4478 88.9349H39.7438C36.8984 88.9349 34.5918 86.6314 34.5918 83.7899C34.5918 80.9484 36.8984 78.6449 39.7438 78.6449H69.1838C72.0292 78.6449 74.3358 76.3414 74.3358 73.4999C74.3358 70.6584 72.0292 68.3549 69.1838 68.3549H50.7838C47.9384 68.3549 45.6318 66.0514 45.6318 63.2099C45.6318 60.3684 47.9384 58.0649 50.7838 58.0649H80.2238C77.3784 58.0649 75.0718 55.7614 75.0718 52.9199C75.0718 50.0784 77.3784 47.7749 80.2238 47.7749H152.352ZM152.352 68.3549C155.197 68.3549 157.504 70.6584 157.504 73.4999C157.504 76.3414 155.197 78.6449 152.352 78.6449C149.506 78.6449 147.2 76.3414 147.2 73.4999C147.2 70.6584 149.506 68.3549 152.352 68.3549Z" fill="#E7EFF5" />
      <path fillRule="evenodd" clipRule="evenodd" d="M113.095 47.0399L119.948 96.9105L120.564 101.914C120.762 103.526 119.615 104.992 118.001 105.19L74.9081 110.469C73.2946 110.666 71.8257 109.52 71.6273 107.908L64.9841 53.9322C64.8849 53.1263 65.4585 52.3929 66.2653 52.2941C66.2704 52.2934 66.2755 52.2928 66.2806 52.2923L69.8557 51.8922M72.748 51.5746L76.124 51.1967L72.748 51.5746Z" fill="white" />
      <path d="M114.333 46.8701C114.239 46.1862 113.608 45.7078 112.924 45.8015C112.24 45.8953 111.762 46.5257 111.856 47.2097L114.333 46.8701ZM119.948 96.9105L121.188 96.7583C121.188 96.7524 121.187 96.7466 121.186 96.7408L119.948 96.9105ZM120.564 101.914L121.804 101.762L120.564 101.914ZM118.001 105.19L118.154 106.431L118.001 105.19ZM74.9081 110.469L75.0605 111.709L74.9081 110.469ZM71.6273 107.908L72.868 107.756L71.6273 107.908ZM64.9841 53.9322L63.7434 54.0845L64.9841 53.9322ZM66.2806 52.2923L66.42 53.5345L66.2806 52.2923ZM69.9951 53.1344C70.6812 53.0577 71.175 52.4393 71.098 51.7532C71.021 51.0672 70.4024 50.5733 69.7163 50.65L69.9951 53.1344ZM72.6086 50.3324C71.9225 50.4092 71.4288 51.0276 71.5058 51.7136C71.5828 52.3997 72.2014 52.8936 72.8875 52.8168L72.6086 50.3324ZM76.2634 52.4389C76.9495 52.3622 77.4432 51.7438 77.3662 51.0577C77.2892 50.3717 76.6706 49.8778 75.9845 49.9546L76.2634 52.4389ZM111.856 47.2097L118.709 97.0803L121.186 96.7408L114.333 46.8701L111.856 47.2097ZM118.707 97.0628L119.323 102.066L121.804 101.762L121.188 96.7583L118.707 97.0628ZM119.323 102.066C119.437 102.991 118.778 103.836 117.849 103.949L118.154 106.431C120.451 106.149 122.087 104.06 121.804 101.762L119.323 102.066ZM117.849 103.949L74.7557 109.228L75.0605 111.709L118.154 106.431L117.849 103.949ZM74.7557 109.228C73.8261 109.342 72.9819 108.681 72.868 107.756L70.3866 108.06C70.6695 110.358 72.763 111.991 75.0605 111.709L74.7557 109.228ZM72.868 107.756L66.2248 53.7799L63.7434 54.0845L70.3866 108.06L72.868 107.756ZM66.2248 53.7799C66.2101 53.6605 66.2951 53.5497 66.4177 53.5347L66.1129 51.0534C64.6219 51.236 63.5597 52.5921 63.7434 54.0845L66.2248 53.7799ZM66.4177 53.5347C66.4184 53.5346 66.4192 53.5345 66.42 53.5345L66.1412 51.0501C66.1317 51.0511 66.1223 51.0522 66.1129 51.0534L66.4177 53.5347ZM66.42 53.5345L69.9951 53.1344L69.7163 50.65L66.1412 51.0501L66.42 53.5345ZM72.8875 52.8168L76.2634 52.4389L75.9845 49.9546L72.6086 50.3324L72.8875 52.8168Z" fill="#00B2EB" />
      <path fillRule="evenodd" clipRule="evenodd" d="M111.239 50.1779L117.436 95.3685L117.993 99.9021C118.173 101.363 117.147 102.69 115.701 102.867L77.1041 107.6C75.6589 107.777 74.3417 106.737 74.1622 105.277L68.2284 57.0158C68.0936 55.9195 68.8731 54.9218 69.9695 54.7873L74.2203 54.2661" fill="#E8F0FE" />
      <path d="M79.7607 40.75C79.7607 39.2312 80.992 38 82.5107 38H114.549C115.277 38 115.976 38.289 116.492 38.8035L125.765 48.0591C126.282 48.5749 126.573 49.2752 126.573 50.0055V94.49C126.573 96.0088 125.342 97.24 123.823 97.24H82.5107C80.992 97.24 79.7607 96.0088 79.7607 94.49V40.75Z" fill="white" stroke="#00B2EB" strokeWidth="2.5" />
      <path d="M115.311 38.5161V47.04C115.311 48.2578 116.299 49.245 117.519 49.245H123.358" stroke="#00B2EB" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M86.8481 86.7301H105.984M86.8481 49.2451H105.984H86.8481ZM86.8481 58.0651H118.496H86.8481ZM86.8481 67.6201H118.496H86.8481ZM86.8481 77.1751H118.496H86.8481Z" stroke="#00B2EB" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
};

export default DraftNoPreviewIllustrator;
