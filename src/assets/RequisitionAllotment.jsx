import React from 'react';

const RequisitionAllotment = () => (

  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M5.99609 10.9997H11.9986" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M5.99609 7.9985H8.99734" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M7.49672 17.0022V17.0022C6.66807 17.0019 5.9964 16.3302 5.99609 15.5016V15.5016C5.9964 14.673 6.66807 14.0013 7.49672 14.001V14.001C8.32537 14.0013 8.99704 14.673 8.99734 15.5016V15.5016C8.99704 16.3302 8.32537 17.0019 7.49672 17.0022V17.0022Z" stroke="#E83A7A" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M12.5002 17.2522H15.0013V21.0038H12.5002C12.224 21.0038 12 20.7798 12 20.5036V17.7524C12 17.4761 12.224 17.2522 12.5002 17.2522Z" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M15.5002 13.0005H17.501C17.7773 13.0005 18.0013 13.2244 18.0013 13.5007V21.0038H15V13.5007C15 13.2244 15.224 13.0005 15.5002 13.0005Z" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M18.5022 15.2515H20.503C20.7793 15.2515 21.0032 15.4754 21.0032 15.7517V20.5037C21.0032 20.7799 20.7793 21.0039 20.503 21.0039H18.002V15.7517C18.002 15.4754 18.2259 15.2515 18.5022 15.2515Z" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M8.99859 21.0038H4.99693C3.8919 21.0038 2.99609 20.108 2.99609 19.003V4.99717C2.99609 3.89214 3.8919 2.99634 4.99693 2.99634H13.1719C13.7026 2.99634 14.2115 3.20714 14.5867 3.58237L17.4163 6.41197C17.7915 6.7872 18.0023 7.29612 18.0023 7.82678V9.99925" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default RequisitionAllotment;
