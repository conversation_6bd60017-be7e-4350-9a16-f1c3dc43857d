import React from 'react';

const SendLetter = () => {
  return (

    <svg width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14.375 10.6704V6.83095C14.375 6.30111 14.805 5.87109 15.3349 5.87109H23.0137C23.5435 5.87109 23.9736 6.30111 23.9736 6.83095V10.6704" stroke="#E82C78" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M14.3746 19.3106H12.4549C11.3942 19.3106 10.5352 18.4515 10.5352 17.3909V12.5916C10.5352 11.5309 11.3942 10.6719 12.4549 10.6719H25.8928C26.9535 10.6719 27.8126 11.5309 27.8126 12.5916V17.3909C27.8126 18.4515 26.9535 19.3106 25.8928 19.3106H23.9731" stroke="#E82C78" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path fillRule="evenodd" clipRule="evenodd" d="M14.375 16.9375H23.9736V22.8886C23.9736 23.4184 23.5435 23.8485 23.0137 23.8485H15.3349C14.805 23.8485 14.375 23.4184 14.375 22.8886V16.9375Z" stroke="#E82C78" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M14.375 13.8041H15.3349" stroke="#E82C78" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M11.2385 5.87109H10.6065H8.62199C6.72517 5.87109 5.1875 7.40877 5.1875 9.30558V19.3986" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M17.0408 27.1289L8.47248 27.1289C6.65824 27.1289 5.1875 25.6582 5.1875 23.8439L5.1875 18.3005" stroke="#09327B" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>

  );
};

export default SendLetter;
