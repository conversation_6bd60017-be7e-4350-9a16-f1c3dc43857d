import React from 'react';

const Merge = () => (

  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16 18H18C19.1046 18 20 17.1046 20 16V5.00003C20 3.89546 19.1046 3.00003 18 3.00003H9C7.89543 3.00003 7 3.89546 7 5.00003V7.00003" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M15.3593 9.47044L13.5296 7.64073C13.1194 7.2305 12.563 7.00003 11.9828 7.00003H6C4.89543 7.00003 4 7.89546 4 9.00003V19C4 20.1046 4.89543 21 6 21H14C15.1046 21 16 20.1046 16 19V11.0172C16 10.4371 15.7695 9.88068 15.3593 9.47044Z" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M16 11.5H13C12.1716 11.5 11.5 10.8285 11.5 10V7.00003" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default Merge;
