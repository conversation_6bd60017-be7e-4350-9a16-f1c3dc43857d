import React from 'react';

const UserRoundFilledIcon = () => {
  return (
    <svg
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="40" height="40" rx="20" fill="#00B2EC" fillOpacity="0.2" />
      <path
        d="M26 27L25.6351 26.1098C25.4504 25.658 25.1647 25.253 24.7976 24.9228L24.731 24.8625C24.1539 24.3427 23.3967 24.0526 22.6096 24.0526H17.3892C16.6022 24.0526 15.8438 24.3427 15.2667 24.8614L15.2001 24.9217C14.8341 25.2519 14.5484 25.6568 14.3626 26.1086L14 27"
        stroke="#00B2EC"
        strokeWidth="1.59795"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M22.7456 14.187C24.2613 15.7691 24.2613 18.3349 22.7456 19.9182C21.2299 21.5016 18.7718 21.5003 17.2549 19.9182C15.738 18.3362 15.7392 15.7704 17.2549 14.187C18.7706 12.6037 21.2287 12.605 22.7456 14.187Z"
        stroke="#00B2EC"
        strokeWidth="1.59795"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default UserRoundFilledIcon;
