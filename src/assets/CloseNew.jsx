import React from 'react';

const CloseNew = ({
  fill = '#F2F4F7', strokeWidth = '1.5', stroke = '#323232', w = '33', h = '32', svgW, svgH
}) => (

  <svg width={svgW || w} height={svgH || h} viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect x="0.5" width={w} height={h} rx="16" fill={fill} />
    <path d="M20.0374 12.4609L12.9624 19.5359" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
    <path d="M20.0374 19.5359L12.9624 12.4609" stroke={stroke} strokeWidth={strokeWidth} strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default CloseNew;
