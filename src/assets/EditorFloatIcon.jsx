const EditorFloatIcon = () => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.75 21H5.25C4.007 21 3 19.993 3 18.75V14.25C3 13.007 4.007 12 5.25 12H9.75C10.993 12 12 13.007 12 14.25V18.75C12 19.993 10.993 21 9.75 21Z"
        stroke="#E8EFF4"
        strokeWidth="2.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.001 10L17.001 7"
        stroke="#E8EFF4"
        strokeWidth="2.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17.001 10V7H14.001"
        stroke="#E8EFF4"
        strokeWidth="2.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15 21H17C19.209 21 21 19.209 21 17V7C21 4.791 19.209 3 17 3H7C4.791 3 3 4.791 3 7V9"
        stroke="#E8EFF4"
        strokeWidth="2.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default EditorFloatIcon;
