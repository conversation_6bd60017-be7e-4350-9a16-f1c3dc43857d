import React from 'react';

const UnMerge = () => (

  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17 17V19C17.0001 19.5305 16.7895 20.0393 16.4144 20.4144C16.0393 20.7895 15.5305 21.0002 15 21H6C5.46952 21.0002 4.96073 20.7895 4.58563 20.4144C4.21052 20.0393 3.99985 19.5305 4 19V8.00003C3.99985 7.46955 4.21052 6.96076 4.58563 6.58566C4.96073 6.21055 5.46952 5.99988 6 6.00003H8" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path fillRule="evenodd" clipRule="evenodd" d="M20 15V7.02846C20 6.49802 19.7893 5.98931 19.4142 5.61424L17.3858 3.58582C17.0107 3.21074 16.502 3.00003 15.9716 3.00003H10C8.89543 3.00003 8 3.89546 8 5.00003V15C8 16.1046 8.89543 17 10 17H18C19.1046 17 20 16.1046 20 15Z" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.75 12.75L15.25 10.25" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M12.75 10.25L15.25 12.75" stroke="#E82C78" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path d="M20 7.50003H17C16.1716 7.50003 15.5 6.82846 15.5 6.00003V3.00003" stroke="#09327B" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>

);

export default UnMerge;
