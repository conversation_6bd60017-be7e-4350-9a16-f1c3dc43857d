name: PROD Deployment
on:
  push:
    branches:
      - main

jobs:
  call-build-and-deploy:
    uses: ksmartikm/ksm-devops-shared/.github/workflows/fe-build-and-deploy-prod.yml@main
    with:
      app-name: ksm-kfm-frontend
      artifacts-bucket: ksm-artifacts-prod
      app-bucket: ksm-webapp-prod
      env-conf: prod.json
      ui-path: file-management
    secrets:
      aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
      aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
      npm-auth-token: ${{ secrets.MAVEN_PASSWORD }}
      cloud-distribution-id: ${{ secrets.PROD_CLOUDFRONT_DIST_ID }}
      github-token: ${{ secrets.MAVEN_PASSWORD }}
      
