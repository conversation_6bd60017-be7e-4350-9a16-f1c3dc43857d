name: Staging Deployment
on:
  push:
    branches:
      - release

jobs:
  call-build-and-deploy:
    uses: ksmartikm/ksm-devops-shared/.github/workflows/fe-build-and-deploy.yml@main
    with:
      app-name: ksm-bp-frontend
      artifacts-bucket: ksm-artifacts-staging
      app-bucket: ksm-webapp-staging
      env-conf: staging.json
      ui-path: file-management
    secrets:
      aws-access-key-id: ${{ secrets.PROD_AWS_ACCESS_KEY_ID }}
      aws-secret-access-key: ${{ secrets.PROD_AWS_SECRET_ACCESS_KEY }}
      npm-auth-token: ${{ secrets.MAVEN_PASSWORD }}
      cloud-distribution-id: ${{ secrets.STAGING_CLOUDFRONT_DIST_ID }}