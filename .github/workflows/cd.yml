name: Continuous Deployment
on:
  push:
    branches:
      - develop

jobs:
  call-build-and-deploy:
    uses: ksmartikm/ksm-devops-shared/.github/workflows/fe-build-and-deploy.yml@main
    with:
      app-name: ksm-kfm-frontend
      artifacts-bucket: ksm-artifacts-dev
      app-bucket: ksm-webapp-dev
      env-conf: dev.json
      ui-path: file-management
    secrets:
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      npm-auth-token: ${{ secrets.MAVEN_PASSWORD }}
      cloud-distribution-id: ${{ secrets.DEV_CLOUDFRONT_DIST_ID }}